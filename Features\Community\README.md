# Community社区功能模块说明文档

## 概述
Community模块是HarmonyOS应用的社区功能模块，主要用于展示开发者社区内容、技术文章、经验分享和动态信息。该模块采用MVVM架构模式，提供了丰富的社区内容展示和交互功能。

## 目录结构

### 根目录文件

#### 配置文件
- **BuildProfile.ets** - 构建配置文件
  - 用途：定义构建时的版本信息、调试模式等常量
  - 调用方法：`import BuildProfile from './BuildProfile'`
  - 数据更改：修改HAR_VERSION、BUILD_MODE_NAME、DEBUG、TARGET_NAME等常量

- **Index.ets** - 模块入口文件
  - 用途：导出模块的主要组件和模型
  - 调用方法：`import { ExplorationView, DiscoverModel } from '@ohos/community'`
  - 数据更改：添加新的导出组件时在此文件中添加export语句

- **build-profile.json5** - 构建配置
  - 用途：定义模块的构建选项、混淆规则、目标平台等
  - 调用方法：由构建系统自动读取
  - 数据更改：修改apiType、buildOption、targets等配置项

- **hvigorfile.ts** - Hvigor构建脚本
  - 用途：定义构建任务和插件配置
  - 调用方法：由Hvigor构建系统自动执行
  - 数据更改：在plugins数组中添加自定义插件

- **oh-package.json5** - 包配置文件
  - 用途：定义模块名称、版本、依赖关系
  - 调用方法：由包管理器自动读取
  - 数据更改：使用包管理器命令添加/删除依赖，不建议手动编辑

#### 规则文件
- **consumer-rules.txt** - 消费者混淆规则
  - 用途：定义哪些文件在混淆时需要保持原样
  - 数据更改：添加需要保持的文件路径

- **obfuscation-rules.txt** - 混淆规则配置
  - 用途：定义代码混淆的具体规则和选项
  - 数据更改：修改混淆选项如-enable-property-obfuscation等

### src/main目录结构

#### module.json5
- 用途：定义模块的基本信息和路由映射
- 调用方法：由系统自动读取
- 数据更改：修改模块名称、类型、支持的设备类型

#### ets目录 - 主要源码目录

##### view目录 - 页面视图
- **ExplorationView.ets** - 探索主视图
  - 用途：社区主页面，展示横幅、开发者内容、体验分享、动态信息
  - 调用方法：作为社区页面的主组件使用
  - 数据更改：通过ExplorationViewModel管理数据状态
  - 功能特性：支持下拉刷新、分页加载、响应式布局

- **ArticleDetailView.ets** - 文章详情视图
  - 用途：展示文章的详细内容页面
  - 调用方法：通过路由导航到文章详情页面
  - 数据更改：通过ArticleDetailViewModel管理文章数据
  - 功能特性：支持Web内容展示、分享、收藏等功能

- **BannerDetailView.ets** - 横幅详情视图
  - 用途：展示横幅点击后的详细内容
  - 调用方法：从横幅卡片点击跳转
  - 数据更改：接收横幅数据参数进行展示
  - 功能特性：支持富媒体内容展示

##### component目录 - UI组件
- **DeveloperCard.ets** - 开发者卡片
  - 用途：展示开发者相关内容的轮播卡片
  - 调用方法：`<DeveloperCard discoverContents={developerData} handleItemClick={onItemClick} />`
  - 数据更改：传入DiscoverContent数组数据
  - 功能特性：支持轮播展示、点击交互、响应式适配

- **DeveloperItem.ets** - 开发者项目
  - 用途：单个开发者内容项的展示组件
  - 调用方法：在DeveloperCard中使用
  - 数据更改：接收DiscoverContent数据
  - 功能特性：展示开发者头像、标题、描述等信息

- **ExperienceCard.ets** - 体验卡片
  - 用途：展示技术体验和教程内容的卡片
  - 调用方法：`<ExperienceCard discoverContents={experienceData} handleItemClick={onItemClick} />`
  - 数据更改：传入体验内容数据数组
  - 功能特性：支持横向滚动、放大效果、动画交互

- **ExperienceItem.ets** - 体验项目
  - 用途：单个体验内容的展示组件
  - 调用方法：在ExperienceCard中使用
  - 数据更改：接收体验内容数据
  - 功能特性：展示体验封面、标题、描述等

- **FeedCard.ets** - 动态卡片
  - 用途：展示社区动态和资讯内容
  - 调用方法：`<FeedCard discoverContents={feedData} handleItemClick={onItemClick} />`
  - 数据更改：传入动态内容数据
  - 功能特性：支持多种媒体类型、时间线展示

- **FeedItem.ets** - 动态项目
  - 用途：单个动态内容的展示组件
  - 调用方法：在FeedCard中使用
  - 数据更改：接收动态内容数据
  - 功能特性：展示动态图片、文字、时间等信息

- **ArticleWebComponent.ets** - 文章Web组件
  - 用途：用于展示Web格式的文章内容
  - 调用方法：在文章详情页面中使用
  - 数据更改：传入文章URL或HTML内容
  - 功能特性：支持Web内容渲染、JavaScript交互

##### model目录 - 数据模型
- **DiscoverData.ets** - 发现数据模型
  - 用途：定义社区内容的数据结构
  - 调用方法：`import { DiscoverContent, DiscoverCardData, DiscoverData } from './model/DiscoverData'`
  - 数据结构：
    ```typescript
    // 发现内容类
    class DiscoverContent {
      id: number;                    // 内容ID
      type: ArticleTypeEnum;         // 文章类型
      mediaType: MediaTypeEnum;      // 媒体类型
      mediaUrl: string;              // 媒体URL
      title: string;                 // 标题
      subTitle: string;              // 副标题
      desc: string;                  // 描述
      publishTime: string;           // 发布时间
      author: string;                // 作者
      detailsUrl: string;            // 详情URL
      urlData: string;               // URL数据
    }
    
    // 发现卡片数据类
    class DiscoverCardData {
      id: number;                    // 卡片ID
      name: string;                  // 卡片名称
      type: ArticleTypeEnum;         // 卡片类型
      contents: DiscoverContent[];   // 内容数组
    }
    
    // 发现数据类
    class DiscoverData {
      bannerInfos?: BannerData[];    // 横幅信息
      discoveryData: DiscoverCardData[]; // 发现数据
    }
    ```

- **DiscoverModel.ets** - 发现数据模型
  - 用途：管理社区内容的数据获取和处理
  - 调用方法：`DiscoverModel.getInstance()`
  - 主要方法：
    ```typescript
    class DiscoverModel {
      // 获取发现页面数据
      getDiscoverPage(): Promise<DiscoverData>
      
      // 刷新数据
      refreshData(): Promise<void>
      
      // 获取特定类型内容
      getContentByType(type: ArticleTypeEnum): Promise<DiscoverContent[]>
    }
    ```

##### service目录 - 服务层
- **DiscoverService.ets** - 发现服务
  - 用途：提供社区内容的数据请求和缓存服务
  - 调用方法：在Model层中调用服务方法
  - 主要方法：
    ```typescript
    class DiscoverService {
      // 通过模拟获取示例列表
      getSampleListByMock(): Promise<DiscoverData>
      
      // 获取发现页面数据
      getDiscoverPage(): Promise<DiscoverData>
      
      // 通过偏好设置获取数据
      getDiscoveryPageByPreference(): Promise<DiscoverData>
      
      // 保存数据到偏好设置
      saveDiscoveryPageToPreference(data: DiscoverData): Promise<void>
    }
    ```

##### viewmodel目录 - 视图模型
- **ExplorationViewModel.ets** - 探索视图模型
  - 用途：管理探索页面的状态和业务逻辑
  - 调用方法：`ExplorationViewModel.getInstance()`
  - 主要功能：
    - 管理页面状态和数据加载
    - 处理用户交互事件
    - 控制页面导航和跳转
    - 管理横幅和内容数据

- **ExplorationState.ets** - 探索状态
  - 用途：定义探索页面的状态数据结构
  - 调用方法：在ViewModel中使用
  - 状态属性：
    - 加载状态
    - 内容数据
    - 错误信息
    - 分页信息

- **ExplorationDetailState.ets** - 探索详情状态
  - 用途：定义探索详情页面的状态
  - 调用方法：在详情页面ViewModel中使用
  - 状态属性：详情内容、加载状态、错误信息

- **ArticleDetailViewModel.ets** - 文章详情视图模型
  - 用途：管理文章详情页面的状态和交互
  - 调用方法：在文章详情页面中实例化
  - 主要功能：
    - 加载文章内容
    - 处理分享和收藏
    - 管理评论和互动

##### common目录 - 通用常量
- **DiscoveryConstant.ets** - 发现常量
  - 用途：定义社区模块使用的常量值
  - 调用方法：`import { DiscoveryConstant } from './common/DiscoveryConstant'`
  - 常量定义：
    ```typescript
    class DiscoveryConstant {
      static DEVELOPER_ITEM_HEIGHT: number = 256; // 开发者项目高度
    }
    ```

## 使用方法

### 1. 导入模块
```typescript
import { ExplorationView, DiscoverModel } from '@ohos/community';
```

### 2. 使用探索视图
```typescript
@Entry
@Component
struct CommunityPage {
  build() {
    ExplorationView()
  }
}
```

### 3. 获取社区数据
```typescript
const model = DiscoverModel.getInstance();
const data = await model.getDiscoverPage();
```

### 4. 处理内容点击事件
```typescript
// 在组件中定义点击处理函数
handleItemClick = (content: DiscoverContent) => {
  // 根据内容类型进行不同处理
  switch (content.type) {
    case ArticleTypeEnum.ARTICLE:
      // 跳转到文章详情
      router.pushUrl({
        url: 'pages/ArticleDetailPage',
        params: { articleData: content }
      });
      break;
    case ArticleTypeEnum.VIDEO:
      // 播放视频
      this.playVideo(content.mediaUrl);
      break;
    default:
      // 默认处理
      break;
  }
};
```

## 数据流向

1. **数据获取**：DiscoverService从网络或本地获取社区数据
2. **数据处理**：DiscoverModel处理和转换数据
3. **状态管理**：ExplorationViewModel管理UI状态
4. **视图渲染**：ExplorationView根据状态渲染UI
5. **用户交互**：用户操作触发ViewModel中的方法
6. **状态更新**：ViewModel更新状态，触发视图重新渲染

## 扩展开发

### 添加新内容类型
1. 在DiscoverData.ets中扩展ArticleTypeEnum枚举
2. 创建对应的组件和视图
3. 在ExplorationView中添加新类型的处理逻辑
4. 更新服务层的数据获取逻辑

### 自定义卡片样式
1. 创建新的卡片组件
2. 在ExplorationView中注册新组件
3. 更新数据模型支持新的卡片类型
4. 添加相应的样式和交互逻辑

### 添加新的交互功能
1. 在ViewModel中添加新的事件处理方法
2. 在组件中绑定新的交互事件
3. 更新状态管理逻辑
4. 添加相应的UI反馈

## 依赖关系

- @ohos/common - 通用工具和组件
- @ohos/commonbusiness - 通用业务组件
- @kit.AbilityKit - 系统能力工具包
- @kit.BasicServicesKit - 基础服务工具包
- @kit.ArkUI - 方舟UI工具包

## 详细组件说明

### 核心组件详解

#### ExplorationView - 探索主视图
**文件位置**：`src/main/ets/view/ExplorationView.ets`

**功能描述**：
- 社区主页面，展示多种类型的内容卡片
- 支持横幅轮播、开发者内容、体验分享、动态信息
- 响应式布局适配不同屏幕尺寸
- 支持下拉刷新和分页加载

**使用方法**：
```typescript
@Entry
@Component
struct CommunityHomePage {
  build() {
    ExplorationView()
  }
}
```

**属性配置**：
- `globalInfoModel`: 全局信息模型，监听断点变化
- `systemColorMode`: 系统颜色模式，支持深色模式
- `explorationState`: 探索页面状态数据
- `listScroller`: 列表滚动控制器

**数据更新方法**：
```typescript
// 刷新社区内容
const viewModel = ExplorationViewModel.getInstance();
viewModel.refreshData();

// 加载更多内容
viewModel.loadMoreData();

// 处理内容点击
viewModel.handleContentClick(content);
```

#### DeveloperCard - 开发者卡片
**文件位置**：`src/main/ets/component/DeveloperCard.ets`

**功能描述**：
- 展示开发者相关内容的轮播卡片
- 支持多个开发者内容的横向滑动
- 响应式适配不同设备尺寸
- 提供点击交互功能

**使用方法**：
```typescript
DeveloperCard({
  discoverContents: this.developerContents,
  handleItemClick: (content: DiscoverContent) => {
    // 处理开发者内容点击
    this.navigateToDetail(content);
  }
})
```

**属性说明**：
- `discoverContents`: 开发者内容数组
- `handleItemClick`: 点击处理函数
- `globalInfoModel`: 全局信息模型

**样式配置**：
```typescript
// 开发者项目比例常量
const DEVELOPER_ITEM_RATIO = 960 / 1312;
const DEVELOPER_ITEM_RATIO_VERDE = 240 / 408;

// 根据设备类型调整显示比例
if (deviceInfo.productSeries === ProductSeriesEnum.VERDE) {
  // Verde设备使用特殊比例
  itemRatio = DEVELOPER_ITEM_RATIO_VERDE;
} else {
  // 其他设备使用标准比例
  itemRatio = DEVELOPER_ITEM_RATIO;
}
```

#### ExperienceCard - 体验卡片
**文件位置**：`src/main/ets/component/ExperienceCard.ets`

**功能描述**：
- 展示技术体验和教程内容
- 支持横向滚动和放大效果
- 提供动画交互体验
- 自适应内容宽度计算

**使用方法**：
```typescript
ExperienceCard({
  discoverContents: this.experienceContents,
  handleItemClick: (content: DiscoverContent) => {
    // 处理体验内容点击
    this.openExperience(content);
  }
})
```

**动画效果**：
```typescript
// 放大动画配置
const ENLARGE_COEFFICIENTS = 2;
const SHOW_COUNT = 3;

// 计算项目宽度
calculateItemWidth() {
  if (this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) {
    this.componentWidth = this.globalInfoModel.deviceWidth * 0.8;
    this.itemWidth = (this.componentWidth - ROW_PADDING) / SHOW_COUNT;
  } else {
    this.itemWidth = this.globalInfoModel.deviceWidth * 0.7;
  }
}

// 处理滚动事件
onScroll(xOffset: number, yOffset: number) {
  // 根据滚动位置计算放大索引
  const index = Math.round(xOffset / this.itemWidth);
  this.enlargeIndex = index;
}
```

#### FeedCard - 动态卡片
**文件位置**：`src/main/ets/component/FeedCard.ets`

**功能描述**：
- 展示社区动态和资讯内容
- 支持多种媒体类型展示
- 时间线式的内容排列
- 支持点赞、评论等交互

**使用方法**：
```typescript
FeedCard({
  discoverContents: this.feedContents,
  handleItemClick: (content: DiscoverContent) => {
    // 处理动态内容点击
    this.viewFeedDetail(content);
  }
})
```

### 数据模型详解

#### DiscoverData - 发现数据模型
**文件位置**：`src/main/ets/model/DiscoverData.ets`

**数据结构详解**：
```typescript
// 文章类型枚举
enum ArticleTypeEnum {
  UNKNOWN = 0,        // 未知类型
  ARTICLE = 1,        // 文章
  VIDEO = 2,          // 视频
  TUTORIAL = 3,       // 教程
  NEWS = 4,           // 新闻
  DEVELOPER = 5,      // 开发者内容
  EXPERIENCE = 6,     // 体验分享
  FEED = 7           // 动态
}

// 发现内容类
class DiscoverContent {
  id: number;                    // 内容唯一标识
  type: ArticleTypeEnum;         // 内容类型
  mediaType: MediaTypeEnum;      // 媒体类型（图片/视频）
  mediaUrl: string;              // 媒体资源URL
  title: string;                 // 内容标题
  subTitle: string;              // 内容副标题
  desc: string;                  // 内容描述
  publishTime: string;           // 发布时间
  author: string;                // 作者信息
  detailsUrl: string;            // 详情页面URL
  urlData: string;               // 附加URL数据
}
```

**使用示例**：
```typescript
// 创建开发者内容
const developerContent: DiscoverContent = {
  id: 1001,
  type: ArticleTypeEnum.DEVELOPER,
  mediaType: MediaTypeEnum.IMAGE,
  mediaUrl: 'images/developer_avatar.png',
  title: 'HarmonyOS开发技巧分享',
  subTitle: '高级开发者',
  desc: '分享HarmonyOS应用开发的最佳实践和技巧',
  publishTime: '2024-01-15 10:30:00',
  author: '张三',
  detailsUrl: '/developer/1001',
  urlData: ''
};

// 创建体验内容
const experienceContent: DiscoverContent = {
  id: 2001,
  type: ArticleTypeEnum.EXPERIENCE,
  mediaType: MediaTypeEnum.VIDEO,
  mediaUrl: 'videos/experience_demo.mp4',
  title: '组件库使用体验',
  subTitle: '实战教程',
  desc: '详细介绍如何使用HarmonyOS组件库开发应用',
  publishTime: '2024-01-16 14:20:00',
  author: '李四',
  detailsUrl: '/experience/2001',
  urlData: ''
};
```

#### DiscoverModel - 发现数据模型
**文件位置**：`src/main/ets/model/DiscoverModel.ets`

**主要功能**：
```typescript
class DiscoverModel {
  private static instance: DiscoverModel;
  private service: DiscoverService;

  // 获取单例实例
  static getInstance(): DiscoverModel {
    if (!DiscoverModel.instance) {
      DiscoverModel.instance = new DiscoverModel();
    }
    return DiscoverModel.instance;
  }

  // 获取发现页面数据
  async getDiscoverPage(): Promise<DiscoverData> {
    try {
      // 首先尝试从本地缓存获取
      const cachedData = await this.service.getDiscoveryPageByPreference();
      if (cachedData && this.isDataValid(cachedData)) {
        return cachedData;
      }

      // 缓存无效时从网络获取
      const networkData = await this.service.getDiscoverPage();

      // 保存到本地缓存
      await this.service.saveDiscoveryPageToPreference(networkData);

      return networkData;
    } catch (error) {
      Logger.error('DiscoverModel', 'Failed to get discover page data', error);
      throw error;
    }
  }

  // 按类型筛选内容
  getContentByType(data: DiscoverData, type: ArticleTypeEnum): DiscoverContent[] {
    const result: DiscoverContent[] = [];
    data.discoveryData.forEach(cardData => {
      if (cardData.type === type) {
        result.push(...cardData.contents);
      }
    });
    return result;
  }

  // 搜索内容
  searchContent(data: DiscoverData, keyword: string): DiscoverContent[] {
    const result: DiscoverContent[] = [];
    data.discoveryData.forEach(cardData => {
      cardData.contents.forEach(content => {
        if (content.title.includes(keyword) ||
            content.desc.includes(keyword) ||
            content.author.includes(keyword)) {
          result.push(content);
        }
      });
    });
    return result;
  }

  // 验证数据有效性
  private isDataValid(data: DiscoverData): boolean {
    // 检查数据是否过期（例如：1小时内有效）
    const cacheTime = data.cacheTime || 0;
    const currentTime = Date.now();
    const expireTime = 60 * 60 * 1000; // 1小时

    return (currentTime - cacheTime) < expireTime;
  }
}
```

### 服务层详解

#### DiscoverService - 发现服务
**文件位置**：`src/main/ets/service/DiscoverService.ets`

**网络请求配置**：
```typescript
class DiscoverService {
  private baseUrl: string = 'https://api.example.com/v1/';
  private timeout: number = 10000;

  // 设置API基础URL
  setBaseUrl(url: string): void {
    this.baseUrl = url;
  }

  // 设置请求超时时间
  setTimeout(timeout: number): void {
    this.timeout = timeout;
  }

  // 获取发现页面数据（网络请求）
  async getDiscoverPage(): Promise<DiscoverData> {
    try {
      const response = await fetch(`${this.baseUrl}discover`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + this.getAuthToken()
        },
        timeout: this.timeout
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return this.transformData(data);
    } catch (error) {
      Logger.error('DiscoverService', 'Network request failed', error);
      // 网络请求失败时返回模拟数据
      return this.getSampleListByMock();
    }
  }

  // 数据转换
  private transformData(rawData: any): DiscoverData {
    const discoverData = new DiscoverData();

    // 转换横幅数据
    if (rawData.banners) {
      discoverData.bannerInfos = rawData.banners.map(banner => ({
        id: banner.id,
        title: banner.title,
        imageUrl: banner.image_url,
        linkUrl: banner.link_url
      }));
    }

    // 转换发现数据
    if (rawData.discovery) {
      discoverData.discoveryData = rawData.discovery.map(item => {
        const cardData = new DiscoverCardData();
        cardData.id = item.id;
        cardData.name = item.name;
        cardData.type = this.mapArticleType(item.type);
        cardData.contents = item.contents.map(content => this.mapContent(content));
        return cardData;
      });
    }

    return discoverData;
  }

  // 获取认证令牌
  private getAuthToken(): string {
    // 从本地存储或全局状态获取认证令牌
    return AppStorage.get('authToken') || '';
  }
}
```

**本地缓存管理**：
```typescript
// 保存数据到偏好设置
async saveDiscoveryPageToPreference(data: DiscoverData): Promise<void> {
  try {
    // 添加缓存时间戳
    data.cacheTime = Date.now();

    const preferenceManager = PreferenceManager.getInstance();
    await preferenceManager.setValue(DiscoverTrigger.DISCOVER_PAGE, data);

    Logger.info('DiscoverService', 'Data saved to preference successfully');
  } catch (error) {
    Logger.error('DiscoverService', 'Failed to save data to preference', error);
  }
}

// 从偏好设置获取数据
async getDiscoveryPageByPreference(): Promise<DiscoverData | null> {
  try {
    const preferenceManager = PreferenceManager.getInstance();
    const data = await preferenceManager.getValue<DiscoverData>(DiscoverTrigger.DISCOVER_PAGE);

    if (data) {
      Logger.info('DiscoverService', 'Data loaded from preference successfully');
      return data;
    }

    return null;
  } catch (error) {
    Logger.error('DiscoverService', 'Failed to load data from preference', error);
    return null;
  }
}

// 清除缓存
async clearCache(): Promise<void> {
  try {
    const preferenceManager = PreferenceManager.getInstance();
    await preferenceManager.removeValue(DiscoverTrigger.DISCOVER_PAGE);

    Logger.info('DiscoverService', 'Cache cleared successfully');
  } catch (error) {
    Logger.error('DiscoverService', 'Failed to clear cache', error);
  }
}
```

### 视图模型详解

#### ExplorationViewModel - 探索视图模型
**文件位置**：`src/main/ets/viewmodel/ExplorationViewModel.ets`

**状态管理**：
```typescript
class ExplorationViewModel extends BaseHomeViewModel<ExplorationState> {
  private static instance: ExplorationViewModel;
  private model: DiscoverModel;

  // 获取单例实例
  static getInstance(): ExplorationViewModel {
    if (!ExplorationViewModel.instance) {
      ExplorationViewModel.instance = new ExplorationViewModel();
    }
    return ExplorationViewModel.instance;
  }

  // 初始化数据
  async initializeData(): Promise<void> {
    try {
      this.updateLoadingStatus(LoadingStatus.LOADING);

      const data = await this.model.getDiscoverPage();

      // 更新状态
      this.state.bannerData = data.bannerInfos || [];
      this.state.discoveryData = data.discoveryData;
      this.state.loadingStatus = LoadingStatus.SUCCESS;

      // 通知视图更新
      this.notifyStateChanged();

    } catch (error) {
      this.state.loadingStatus = LoadingStatus.FAILED;
      this.state.errorMessage = error.message;
      this.notifyStateChanged();
    }
  }

  // 刷新数据
  async refreshData(): Promise<void> {
    try {
      // 清除缓存
      await this.model.clearCache();

      // 重新加载数据
      await this.initializeData();

    } catch (error) {
      Logger.error('ExplorationViewModel', 'Failed to refresh data', error);
    }
  }

  // 处理内容点击
  handleContentClick(content: DiscoverContent): void {
    switch (content.type) {
      case ArticleTypeEnum.ARTICLE:
        this.navigateToArticle(content);
        break;
      case ArticleTypeEnum.VIDEO:
        this.playVideo(content);
        break;
      case ArticleTypeEnum.DEVELOPER:
        this.viewDeveloperProfile(content);
        break;
      case ArticleTypeEnum.EXPERIENCE:
        this.openExperience(content);
        break;
      default:
        this.viewGenericContent(content);
        break;
    }
  }

  // 导航到文章详情
  private navigateToArticle(content: DiscoverContent): void {
    const params: ArticleDetailParams = {
      articleId: content.id.toString(),
      title: content.title,
      url: content.detailsUrl
    };

    router.pushUrl({
      url: 'pages/ArticleDetailPage',
      params: params
    });
  }

  // 播放视频
  private playVideo(content: DiscoverContent): void {
    // 实现视频播放逻辑
    const videoParams = {
      videoUrl: content.mediaUrl,
      title: content.title,
      description: content.desc
    };

    router.pushUrl({
      url: 'pages/VideoPlayerPage',
      params: videoParams
    });
  }
}
```

**事件处理**：
```typescript
// 探索事件类型枚举
export enum ExplorationEventType {
  DATA_LOADED = 'dataLoaded',
  REFRESH_SUCCESS = 'refreshSuccess',
  REFRESH_FAILED = 'refreshFailed',
  CONTENT_CLICKED = 'contentClicked',
  ERROR_OCCURRED = 'errorOccurred'
}

// 事件监听和触发
class ExplorationViewModel {
  private eventListeners: Map<string, Function[]> = new Map();

  // 添加事件监听器
  on(eventType: ExplorationEventType, callback: Function): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    this.eventListeners.get(eventType)!.push(callback);
  }

  // 移除事件监听器
  off(eventType: ExplorationEventType, callback: Function): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  // 触发事件
  private emit(eventType: ExplorationEventType, data?: any): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }

  // 通知状态变化
  private notifyStateChanged(): void {
    this.emit(ExplorationEventType.DATA_LOADED, this.state);
  }
}
```

## 资源文件说明

### resources目录结构
```
resources/
├── base/           # 基础资源
│   ├── element/    # 基础元素定义
│   ├── media/      # 图片资源
│   └── profile/    # 配置文件
├── en_US/          # 英文资源
└── zh_CN/          # 中文资源
```

### 多语言资源配置
**中文资源 (zh_CN/element/string.json)**：
```json
{
  "string": [
    {
      "name": "community_name",
      "value": "社区"
    },
    {
      "name": "developer_section",
      "value": "开发者"
    },
    {
      "name": "experience_section",
      "value": "体验分享"
    },
    {
      "name": "feed_section",
      "value": "动态"
    },
    {
      "name": "load_more",
      "value": "加载更多"
    },
    {
      "name": "refresh_success",
      "value": "刷新成功"
    },
    {
      "name": "load_failed",
      "value": "加载失败"
    }
  ]
}
```

**英文资源 (en_US/element/string.json)**：
```json
{
  "string": [
    {
      "name": "community_name",
      "value": "Community"
    },
    {
      "name": "developer_section",
      "value": "Developers"
    },
    {
      "name": "experience_section",
      "value": "Experience"
    },
    {
      "name": "feed_section",
      "value": "Feed"
    },
    {
      "name": "load_more",
      "value": "Load More"
    },
    {
      "name": "refresh_success",
      "value": "Refresh Success"
    },
    {
      "name": "load_failed",
      "value": "Load Failed"
    }
  ]
}
```

### 图片资源管理
**媒体资源 (base/media/)**：
- `ic_developer_placeholder.png` - 开发者头像占位图
- `ic_experience_placeholder.png` - 体验内容占位图
- `ic_feed_placeholder.png` - 动态内容占位图
- `ic_video_play.png` - 视频播放图标
- `ic_article_icon.png` - 文章图标
- `ic_share.png` - 分享图标
- `ic_like.png` - 点赞图标
- `ic_comment.png` - 评论图标

**使用示例**：
```typescript
// 在组件中使用图片资源
Image($r('app.media.ic_developer_placeholder'))
  .width(48)
  .height(48)
  .borderRadius(24)
  .alt($r('app.media.ic_placeholder'))
```

## 性能优化建议

### 1. 图片懒加载
```typescript
// 使用LazyForEach进行内容懒加载
LazyForEach(this.contentDataSource, (item: DiscoverContent) => {
  if (item.type === ArticleTypeEnum.DEVELOPER) {
    DeveloperItem({ discoverContent: item })
  } else if (item.type === ArticleTypeEnum.EXPERIENCE) {
    ExperienceItem({ discoverContent: item })
  } else {
    FeedItem({ discoverContent: item })
  }
}, (item: DiscoverContent) => item.id.toString())
```

### 2. 数据缓存策略
```typescript
// 实现多级缓存
class CacheManager {
  private memoryCache: Map<string, any> = new Map();
  private readonly CACHE_EXPIRE_TIME = 30 * 60 * 1000; // 30分钟

  // 内存缓存
  setMemoryCache(key: string, data: any): void {
    this.memoryCache.set(key, {
      data: data,
      timestamp: Date.now()
    });
  }

  getMemoryCache(key: string): any | null {
    const cached = this.memoryCache.get(key);
    if (cached && (Date.now() - cached.timestamp) < this.CACHE_EXPIRE_TIME) {
      return cached.data;
    }
    this.memoryCache.delete(key);
    return null;
  }

  // 磁盘缓存
  async setDiskCache(key: string, data: any): Promise<void> {
    await PreferenceManager.getInstance().setValue(key, {
      data: data,
      timestamp: Date.now()
    });
  }

  async getDiskCache(key: string): Promise<any | null> {
    const cached = await PreferenceManager.getInstance().getValue(key);
    if (cached && (Date.now() - cached.timestamp) < this.CACHE_EXPIRE_TIME) {
      return cached.data;
    }
    return null;
  }
}
```

### 3. 列表性能优化
```typescript
// 使用虚拟滚动优化长列表
@Component
struct VirtualList {
  @State visibleItems: DiscoverContent[] = [];
  private allItems: DiscoverContent[] = [];
  private itemHeight: number = 120;
  private containerHeight: number = 600;
  private visibleCount: number = Math.ceil(this.containerHeight / this.itemHeight) + 2;

  onScroll(scrollOffset: number): void {
    const startIndex = Math.floor(scrollOffset / this.itemHeight);
    const endIndex = Math.min(startIndex + this.visibleCount, this.allItems.length);

    this.visibleItems = this.allItems.slice(startIndex, endIndex);
  }
}
```

### 4. 图片优化
```typescript
// 图片压缩和格式优化
class ImageOptimizer {
  // 根据设备像素密度选择合适的图片
  getOptimizedImageUrl(baseUrl: string, width: number, height: number): string {
    const density = display.getDefaultDisplaySync().densityPixels;
    const scale = density > 2 ? '@3x' : density > 1 ? '@2x' : '';

    return `${baseUrl}_${width}x${height}${scale}.webp`;
  }

  // 预加载关键图片
  preloadImages(urls: string[]): void {
    urls.forEach(url => {
      const image = new Image();
      image.src = url;
    });
  }
}
```

## 调试和测试

### 日志记录
```typescript
// 统一日志管理
class CommunityLogger {
  private static readonly TAG = '[Community]';

  static debug(message: string, ...args: any[]): void {
    Logger.debug(this.TAG, message, ...args);
  }

  static info(message: string, ...args: any[]): void {
    Logger.info(this.TAG, message, ...args);
  }

  static warn(message: string, ...args: any[]): void {
    Logger.warn(this.TAG, message, ...args);
  }

  static error(message: string, error?: any): void {
    Logger.error(this.TAG, message, error);
  }

  // 性能监控
  static logPerformance(operation: string, startTime: number): void {
    const duration = Date.now() - startTime;
    this.info(`Performance: ${operation} took ${duration}ms`);
  }
}
```

### 单元测试
```typescript
// 测试数据模型
describe('DiscoverModel', () => {
  let model: DiscoverModel;

  beforeEach(() => {
    model = DiscoverModel.getInstance();
  });

  it('should get discover page data', async () => {
    const data = await model.getDiscoverPage();
    expect(data).toBeDefined();
    expect(data.discoveryData).toBeInstanceOf(Array);
  });

  it('should filter content by type', () => {
    const mockData = createMockDiscoverData();
    const developerContent = model.getContentByType(mockData, ArticleTypeEnum.DEVELOPER);

    expect(developerContent.length).toBeGreaterThan(0);
    developerContent.forEach(content => {
      expect(content.type).toBe(ArticleTypeEnum.DEVELOPER);
    });
  });

  it('should search content by keyword', () => {
    const mockData = createMockDiscoverData();
    const results = model.searchContent(mockData, 'HarmonyOS');

    expect(results.length).toBeGreaterThan(0);
    results.forEach(content => {
      expect(
        content.title.includes('HarmonyOS') ||
        content.desc.includes('HarmonyOS') ||
        content.author.includes('HarmonyOS')
      ).toBe(true);
    });
  });
});
```

### 集成测试
```typescript
// 测试视图模型
describe('ExplorationViewModel', () => {
  let viewModel: ExplorationViewModel;

  beforeEach(() => {
    viewModel = ExplorationViewModel.getInstance();
  });

  it('should initialize data successfully', async () => {
    await viewModel.initializeData();

    const state = viewModel.getState();
    expect(state.loadingStatus).toBe(LoadingStatus.SUCCESS);
    expect(state.discoveryData.length).toBeGreaterThan(0);
  });

  it('should handle content click correctly', () => {
    const mockContent: DiscoverContent = {
      id: 1,
      type: ArticleTypeEnum.ARTICLE,
      title: 'Test Article',
      // ... 其他属性
    };

    const navigateSpy = jest.spyOn(router, 'pushUrl');
    viewModel.handleContentClick(mockContent);

    expect(navigateSpy).toHaveBeenCalledWith({
      url: 'pages/ArticleDetailPage',
      params: expect.objectContaining({
        articleId: '1',
        title: 'Test Article'
      })
    });
  });
});
```

## 常见问题解决

### 1. 内容加载失败
**原因**：网络请求超时或数据格式错误
**解决方案**：
```typescript
// 添加重试机制
class RetryableRequest {
  private maxRetries: number = 3;
  private retryDelay: number = 1000;

  async requestWithRetry<T>(requestFn: () => Promise<T>): Promise<T> {
    let lastError: Error;

    for (let i = 0; i < this.maxRetries; i++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error as Error;

        if (i < this.maxRetries - 1) {
          await this.delay(this.retryDelay * Math.pow(2, i));
        }
      }
    }

    throw lastError!;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### 2. 图片加载缓慢
**原因**：图片尺寸过大或网络较慢
**解决方案**：
```typescript
// 图片预加载和渐进式加载
@Component
struct ProgressiveImage {
  @Prop src: string;
  @State loaded: boolean = false;
  @State error: boolean = false;

  build() {
    Stack() {
      // 占位图
      if (!this.loaded && !this.error) {
        Image($r('app.media.ic_placeholder'))
          .width('100%')
          .height('100%')
          .objectFit(ImageFit.Cover)
      }

      // 实际图片
      Image(this.src)
        .width('100%')
        .height('100%')
        .objectFit(ImageFit.Cover)
        .opacity(this.loaded ? 1 : 0)
        .animation({
          duration: 300,
          curve: Curve.EaseInOut
        })
        .onComplete(() => {
          this.loaded = true;
        })
        .onError(() => {
          this.error = true;
        })
    }
  }
}
```

### 3. 滚动性能问题
**原因**：列表项过多或渲染复杂
**解决方案**：
```typescript
// 优化滚动性能
@Component
struct OptimizedList {
  @State items: DiscoverContent[] = [];
  private scroller: Scroller = new Scroller();
  private isScrolling: boolean = false;

  build() {
    List({ scroller: this.scroller }) {
      LazyForEach(this.dataSource, (item: DiscoverContent) => {
        ListItem() {
          // 使用简化的列表项组件
          SimpleListItem({ content: item })
        }
        .reuseId(item.type.toString()) // 启用组件复用
      }, (item: DiscoverContent) => item.id.toString())
    }
    .onScroll(() => {
      if (!this.isScrolling) {
        this.isScrolling = true;
        // 延迟处理滚动事件
        setTimeout(() => {
          this.handleScroll();
          this.isScrolling = false;
        }, 16); // 约60fps
      }
    })
    .cachedCount(5) // 缓存列表项数量
  }

  private handleScroll(): void {
    // 处理滚动逻辑
  }
}
```

### 4. 内存泄漏问题
**原因**：事件监听器未正确清理或循环引用
**解决方案**：
```typescript
// 正确的生命周期管理
@Component
struct CommunityComponent {
  private viewModel: ExplorationViewModel = ExplorationViewModel.getInstance();
  private eventListeners: Function[] = [];

  aboutToAppear(): void {
    // 添加事件监听器
    const listener = (data: any) => this.handleDataUpdate(data);
    this.viewModel.on(ExplorationEventType.DATA_LOADED, listener);
    this.eventListeners.push(() => {
      this.viewModel.off(ExplorationEventType.DATA_LOADED, listener);
    });
  }

  aboutToDisappear(): void {
    // 清理事件监听器
    this.eventListeners.forEach(cleanup => cleanup());
    this.eventListeners = [];

    // 清理其他资源
    this.viewModel.dispose();
  }

  private handleDataUpdate(data: any): void {
    // 处理数据更新
  }
}
```

## 版本更新说明

### v1.0.0 (当前版本)
- 基础社区内容展示功能
- 开发者、体验、动态三大内容模块
- 横幅轮播和内容卡片展示
- 多语言和主题支持
- 响应式布局适配

### 后续版本规划
- 用户登录和个人中心
- 内容点赞、评论、分享功能
- 内容搜索和筛选
- 离线阅读支持
- 推送通知功能
- 内容创作和发布
- 社区互动和关注功能
