# CommonBusiness通用业务模块说明文档

## 概述
CommonBusiness模块是HarmonyOS应用的通用业务组件库，提供了应用中常用的业务组件、数据模型、视图模型和基础视图。该模块采用MVVM架构模式，为其他功能模块提供统一的业务基础设施和可复用的组件。

## 目录结构

### 根目录文件

#### 配置文件
- **BuildProfile.ets** - 构建配置文件
  - 用途：定义构建时的版本信息、调试模式等常量
  - 调用方法：`import BuildProfile from './BuildProfile'`
  - 数据更改：修改HAR_VERSION、BUILD_MODE_NAME、DEBUG、TARGET_NAME等常量

- **Index.ets** - 模块入口文件
  - 用途：导出模块的主要组件、模型和视图模型
  - 调用方法：`import { BannerCard, BaseHomeViewModel, CardData } from '@ohos/commonbusiness'`
  - 数据更改：添加新的导出组件时在此文件中添加export语句

- **build-profile.json5** - 构建配置
  - 用途：定义模块的构建选项、混淆规则、目标平台等
  - 调用方法：由构建系统自动读取
  - 数据更改：修改apiType、buildOption、targets等配置项

- **hvigorfile.ts** - Hvigor构建脚本
  - 用途：定义构建任务和插件配置
  - 调用方法：由Hvigor构建系统自动执行
  - 数据更改：在plugins数组中添加自定义插件

- **oh-package.json5** - 包配置文件
  - 用途：定义模块名称、版本、依赖关系
  - 调用方法：由包管理器自动读取
  - 数据更改：使用包管理器命令添加/删除依赖，不建议手动编辑

#### 规则文件
- **consumer-rules.txt** - 消费者混淆规则
  - 用途：定义哪些文件在混淆时需要保持原样
  - 数据更改：添加需要保持的文件路径

- **obfuscation-rules.txt** - 混淆规则配置
  - 用途：定义代码混淆的具体规则和选项
  - 数据更改：修改混淆选项如-enable-property-obfuscation等

### src/main目录结构

#### module.json5
- 用途：定义模块的基本信息和设备类型支持
- 调用方法：由系统自动读取
- 数据更改：修改模块名称、类型、支持的设备类型

#### ets目录 - 主要源码目录

##### component目录 - 通用业务组件

- **BannerCard.ets** - 横幅卡片组件
  - 用途：展示横幅内容的卡片组件，支持响应式布局
  - 调用方法：
    ```typescript
    BannerCard({
      bannerState: this.bannerState,
      tabViewType: this.tabViewType,
      handleItemClick: (bannerData: BannerData) => {
        // 处理横幅点击事件
      }
    })
    ```
  - 功能特性：
    - 大屏设备显示为水平滚动列表
    - 小屏设备显示为轮播图
    - 支持自动播放和手动滑动
    - 响应式断点适配
    - 指示点显示和动画效果

- **BannerItem.ets** - 横幅项组件
  - 用途：单个横幅内容的展示组件
  - 调用方法：在BannerCard中使用
  - 数据更改：接收BannerData数据
  - 功能特性：展示横幅图片、标题、描述等信息

- **BaseDetailComponent.ets** - 基础详情组件
  - 用途：提供详情页面的基础布局和功能
  - 调用方法：作为详情页面的基础组件使用
  - 功能特性：统一的详情页面布局、导航栏、内容区域

- **FullScreenNavigation.ets** - 全屏导航组件
  - 用途：全屏页面的顶部导航栏组件
  - 调用方法：
    ```typescript
    FullScreenNavigation({
      topNavigationData: this.navigationData,
      tabView: () => {
        // 自定义标签视图
      }
    })
    ```
  - 功能特性：
    - 支持标题显示和缩放动画
    - 标签页视图集成
    - 模糊效果支持
    - 响应式布局适配

- **LoadingMoreItem.ets** - 加载更多项组件
  - 用途：在列表底部显示加载更多或没有更多数据的状态提示
  - 调用方法：
    ```typescript
    LoadingMoreItemBuilder(this.loadingModel)
    ```
  - 功能特性：
    - 自动根据加载状态切换显示内容
    - 支持加载中和无更多数据状态
    - 统一的样式和动画效果

##### model目录 - 数据模型

- **BannerData.ets** - 横幅数据模型
  - 用途：定义横幅相关的数据结构
  - 调用方法：`import { BannerData, BannerTypeEnum } from './model/BannerData'`
  - 数据结构：
    ```typescript
    class BannerData {
      id: number;                    // 横幅唯一标识
      bannerTitle: string;           // 横幅主标题
      bannerSubTitle: string;        // 横幅副标题
      bannerDesc: string;            // 横幅描述
      bannerType: BannerTypeEnum;    // 横幅类型
      bannerValue: string;           // 横幅值
      mediaType: MediaTypeEnum;      // 媒体类型
      mediaUrl: string;              // 媒体URL
      detailsUrl: string;            // 详情URL
      tabViewType: number;           // 标签视图类型
    }
    
    enum BannerTypeEnum {
      COMPONENT = 1,    // 组件类型
      SAMPLE = 2,       // 示例类型
      CODELAB = 3,      // 代码实验室类型
      ARTICLE = 4,      // 文章类型
      UNKNOWN = 0       // 未知类型
    }
    ```

- **CardData.ets** - 卡片数据模型
  - 用途：定义卡片相关的数据结构
  - 调用方法：`import { CardData, CardTypeEnum, MediaTypeEnum } from './model/CardData'`
  - 数据结构：
    ```typescript
    class CardData {
      id: number;                           // 卡片唯一标识
      cardTitle: string;                    // 卡片主标题
      cardSubTitle: string;                 // 卡片副标题
      cardType: CardTypeEnum;               // 卡片类型
      cardStyleType: CardStyleTypeEnum;     // 卡片样式类型
      cardImage: string;                    // 卡片图片
      version: string;                      // 卡片版本
      cardContents: CardContent[];          // 卡片内容列表
    }
    
    class CardContent {
      id: number;                    // 内容唯一标识
      type: CardTypeEnum;            // 内容类型
      mediaType: MediaTypeEnum;      // 媒体类型
      mediaUrl: string;              // 媒体URL
      title: string;                 // 内容标题
      subTitle: string;              // 内容副标题
      desc: string;                  // 内容描述
      detailsUrl?: string;           // 详情URL
      originalUrl?: string;          // 原始URL
    }
    ```

- **FullScreenNavigationData.ets** - 全屏导航数据模型
  - 用途：定义全屏导航的配置数据
  - 调用方法：`import { FullScreenNavigationData } from './model/FullScreenNavigationData'`
  - 数据更改：设置导航标题、背景色、标题颜色等属性

- **RouterParams.ets** - 路由参数接口
  - 用途：定义页面间路由传递的参数接口
  - 调用方法：`import { ArticleDetailParams, ComponentDetailParams } from './model/RouterParams'`
  - 接口定义：
    ```typescript
    interface ComponentDetailParams {
      componentName: string;    // 组件名称
      componentId: number;      // 组件ID
    }
    
    interface SampleDetailParams {
      currentIndex: number;     // 当前索引
      sampleCardId: number;     // 示例卡片ID
    }
    
    interface ArticleDetailParams {
      id: number;               // 文章ID
      isArticle: boolean;       // 是否为文章
      title: string;            // 文章标题
      detailsUrl: string;       // 详情URL
      tabViewType?: number;     // 标签视图类型
    }
    ```

- **TabStatusBarModel.ets** - 标签状态栏模型
  - 用途：定义标签页的状态栏配置
  - 调用方法：`import { TabBarType, TAB_CONTENT_STATUSES } from './model/TabStatusBarModel'`
  - 配置说明：
    ```typescript
    enum TabBarType {
      HOME = 0,        // 首页标签
      SAMPLE = 1,      // 示例标签
      PRACTICE = 2,    // 实践标签
      MINE = 3         // 我的标签
    }
    
    // 标签内容状态栏配置，true为深色，false为浅色
    const TAB_CONTENT_STATUSES: boolean[] = [true, true, true];
    ```

##### view目录 - 基础视图

- **BaseHomeView.ets** - 基础首页视图
  - 用途：通用的首页视图组件，提供统一的加载状态管理
  - 调用方法：
    ```typescript
    BaseHomeView({
      loadingModel: this.loadingModel,
      reloadData: () => {
        // 重新加载数据的回调
      },
      contentView: () => {
        // 自定义内容视图
      },
      topTitleView: () => {
        // 自定义顶部标题视图
      }
    })
    ```
  - 功能特性：
    - 支持成功、失败、加载中、无网络等状态显示
    - 通过BuilderParam接收自定义视图
    - 统一的错误处理和重试机制
    - 响应式布局适配

##### viewmodel目录 - 视图模型

- **BaseHomeViewModel.ets** - 基础首页视图模型
  - 用途：管理首页相关的业务逻辑和状态
  - 调用方法：继承此类创建具体的首页视图模型
  - 主要功能：
    ```typescript
    class BaseHomeViewModel<T extends BaseHomeState> extends BaseVM<BaseHomeState> {
      // 状态管理
      protected state: T;
      protected readonly pageSize: number = 30;
      
      // 构造函数
      constructor(initialState: T);
      
      // 断点变化处理
      handleBreakPointChange(): void;
      
      // 横幅点击处理
      handleBannerItemClick(bannerData: BannerData): void;
      
      // 滚动事件处理
      onScroll(xOffset: number, yOffset: number): void;
      
      // 计算高度参数
      calculateHeightParam(offsetParam: OffsetParam): CalculateHeightParam;
      
      // 页面生命周期
      onPageShow(): void;
      onPageHide(): void;
    }
    ```

- **BaseHomeState.ets** - 基础首页状态
  - 用途：定义首页的状态数据结构
  - 调用方法：继承此类创建具体的状态类
  - 状态属性：
    ```typescript
    class BaseHomeState {
      topNavigationData: FullScreenNavigationData;  // 顶部导航数据
      bannerState: BannerState;                     // 横幅状态
      loadingModel: LoadingModel;                   // 加载模型
    }
    
    class BannerState {
      bannerResource: BannerSource;                 // 横幅资源
      bannerHeight: number;                         // 横幅高度
      bannerScale: number;                          // 横幅缩放
    }
    ```

- **BannerSource.ets** - 横幅数据源
  - 用途：管理横幅数据的数据源
  - 调用方法：`new BannerSource(bannerDataArray)`
  - 主要方法：
    ```typescript
    class BannerSource extends BasicDataSource {
      // 获取数据总数
      totalCount(): number;
      
      // 获取指定位置的数据
      getData(index: number): BannerData;
      
      // 添加数据
      addData(index: number, data: BannerData): void;
      
      // 删除数据
      removeData(index: number): void;
      
      // 更新数据
      updateData(index: number, data: BannerData): void;
    }
    ```

## 使用方法

### 1. 导入模块
```typescript
import {
  BannerCard,
  BaseHomeViewModel,
  BaseHomeView,
  CardData,
  BannerData,
  FullScreenNavigation
} from '@ohos/commonbusiness';
```

### 2. 创建自定义首页视图模型
```typescript
class MyHomeViewModel extends BaseHomeViewModel<MyHomeState> {
  constructor() {
    super(new MyHomeState());
  }
  
  // 重写或扩展基础功能
  async loadData(): Promise<void> {
    // 加载数据逻辑
  }
}
```

### 3. 使用基础首页视图
```typescript
@Entry
@Component
struct MyHomePage {
  private viewModel: MyHomeViewModel = new MyHomeViewModel();
  
  build() {
    BaseHomeView({
      loadingModel: this.viewModel.state.loadingModel,
      reloadData: () => this.viewModel.loadData(),
      contentView: () => {
        this.buildContentView()
      },
      topTitleView: () => {
        FullScreenNavigation({
          topNavigationData: this.viewModel.state.topNavigationData
        })
      }
    })
  }
  
  @Builder
  buildContentView() {
    Column() {
      // 横幅卡片
      BannerCard({
        bannerState: this.viewModel.state.bannerState,
        tabViewType: 0,
        handleItemClick: (bannerData: BannerData) => {
          this.viewModel.handleBannerItemClick(bannerData);
        }
      })
      
      // 其他内容
    }
  }
}
```

### 4. 创建横幅数据
```typescript
// 创建横幅数据
const bannerData: BannerData = {
  id: 1,
  bannerTitle: '组件示例',
  bannerSubTitle: 'HarmonyOS组件库',
  bannerDesc: '丰富的UI组件和示例代码',
  bannerType: BannerTypeEnum.COMPONENT,
  bannerValue: 'component_demo',
  mediaType: MediaTypeEnum.IMAGE,
  mediaUrl: 'images/banner_component.png',
  detailsUrl: '/pages/ComponentDetailPage',
  tabViewType: 0
};

// 创建横幅数据源
const bannerSource = new BannerSource([bannerData]);
```

### 5. 处理路由导航
```typescript
// 组件详情导航
const componentParams: ComponentDetailParams = {
  componentName: 'Button',
  componentId: 1001
};

router.pushUrl({
  url: 'pages/ComponentDetailPage',
  params: componentParams
});

// 文章详情导航
const articleParams: ArticleDetailParams = {
  id: 2001,
  isArticle: true,
  title: 'HarmonyOS开发指南',
  detailsUrl: '/articles/2001',
  tabViewType: 0
};

router.pushUrl({
  url: 'pages/ArticleDetailPage',
  params: articleParams
});
```

## 数据流向

1. **状态初始化**：BaseHomeViewModel初始化BaseHomeState
2. **数据加载**：子类视图模型实现具体的数据加载逻辑
3. **状态更新**：视图模型更新状态，触发UI重新渲染
4. **用户交互**：用户操作触发视图模型中的事件处理方法
5. **页面导航**：通过RouterParams进行页面间的参数传递
6. **生命周期**：视图模型管理页面的生命周期事件

## 扩展开发

### 创建自定义业务组件
1. 在component目录下创建新的组件文件
2. 继承或使用现有的数据模型
3. 在Index.ets中导出新组件
4. 添加相应的样式和交互逻辑

### 扩展数据模型
1. 在model目录下创建新的数据模型文件
2. 定义数据结构和枚举类型
3. 在Index.ets中导出新模型
4. 更新相关的视图模型和组件

### 自定义视图模型
1. 继承BaseHomeViewModel类
2. 定义自己的状态类继承BaseHomeState
3. 实现具体的业务逻辑方法
4. 处理特定的事件和生命周期

## 依赖关系

- @ohos/common - 通用工具和基础组件
- @kit.AbilityKit - 系统能力工具包
- @kit.BasicServicesKit - 基础服务工具包
- @kit.ArkUI - 方舟UI工具包

## 详细组件说明

### 核心组件详解

#### BannerCard - 横幅卡片组件
**文件位置**：`src/main/ets/component/BannerCard.ets`

**功能描述**：
- 展示横幅内容的卡片组件，支持响应式布局
- 在大屏设备上显示为水平滚动列表
- 在小屏设备上显示为轮播图
- 支持自动播放、手动滑动、指示点显示

**属性配置**：
```typescript
@Component
export struct BannerCard {
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel;
  @Prop @Require bannerState: BannerState;           // 横幅状态数据
  @Prop @Require tabViewType: number;                // 标签视图类型
  handleItemClick?: (bannerData: BannerData) => void; // 点击处理函数
}
```

**响应式配置**：
```typescript
// 横幅内边距根据断点类型设置
private bannerPadding: number = new BreakpointType({
  sm: CommonConstants.SPACE_16,    // 小屏16dp
  md: CommonConstants.SPACE_24,    // 中屏24dp
  lg: CommonConstants.SPACE_32     // 大屏32dp
}).getValue(this.globalInfoModel.currentBreakpoint);

// 横幅宽高比常量
const BANNER_ASPECT = 1.75;
const BANNER_DOT_HEIGHT = 2;
const BANNER_DOT_SPACING = 8;
```

**使用示例**：
```typescript
BannerCard({
  bannerState: {
    bannerResource: new BannerSource(this.bannerDataArray),
    bannerHeight: 200,
    bannerScale: 1.0
  },
  tabViewType: TabBarType.HOME,
  handleItemClick: (bannerData: BannerData) => {
    // 根据横幅类型进行不同处理
    switch (bannerData.bannerType) {
      case BannerTypeEnum.COMPONENT:
        this.navigateToComponent(bannerData);
        break;
      case BannerTypeEnum.ARTICLE:
        this.navigateToArticle(bannerData);
        break;
      case BannerTypeEnum.SAMPLE:
        this.navigateToSample(bannerData);
        break;
      default:
        break;
    }
  }
})
```

#### FullScreenNavigation - 全屏导航组件
**文件位置**：`src/main/ets/component/FullScreenNavigation.ets`

**功能描述**：
- 全屏页面的顶部导航栏组件
- 支持标题显示、标签页视图、模糊效果
- 具备响应式布局和动画效果
- 支持标题缩放动画

**属性配置**：
```typescript
@Component
export struct FullScreenNavigation {
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel;
  @Prop topNavigationData: FullScreenNavigationData;  // 导航数据
  @StorageProp('BlurRenderGroup') blurRenderGroup: boolean; // 模糊效果
  @BuilderParam tabView?: () => void;                 // 标签视图构建器
}
```

**导航数据配置**：
```typescript
class FullScreenNavigationData {
  title: string = '';              // 导航标题
  titleScale: number = 1.0;        // 标题缩放比例
  bgColor: string = '#FFFFFF';     // 背景颜色
  titleColor: StatusBarColorType = StatusBarColorType.BLACK; // 标题颜色
}
```

**使用示例**：
```typescript
FullScreenNavigation({
  topNavigationData: {
    title: '首页',
    titleScale: 1.0,
    bgColor: '#FFF1F3F5',
    titleColor: StatusBarColorType.BLACK
  },
  tabView: () => {
    // 自定义标签视图
    Row() {
      ForEach(this.tabItems, (item: TabItem) => {
        Text(item.title)
          .fontSize(16)
          .fontColor(item.selected ? '#007DFF' : '#182431')
          .onClick(() => this.selectTab(item.id))
      })
    }
  }
})
```

#### BaseDetailComponent - 基础详情组件
**文件位置**：`src/main/ets/component/BaseDetailComponent.ets`

**功能描述**：
- 提供详情页面的基础布局和功能
- 统一的详情页面结构和样式
- 支持自定义内容区域
- 集成导航栏和返回功能

**使用示例**：
```typescript
BaseDetailComponent({
  title: '组件详情',
  showBackButton: true,
  onBackClick: () => {
    router.back();
  },
  contentView: () => {
    // 自定义详情内容
    Column() {
      Text('详情内容')
      // 其他详情组件
    }
  }
})
```

### 数据模型详解

#### BannerData - 横幅数据模型
**文件位置**：`src/main/ets/model/BannerData.ets`

**数据结构详解**：
```typescript
@Observed
export class BannerData {
  id: number = 0;                           // 横幅唯一标识
  bannerTitle: string = '';                 // 横幅主标题
  bannerSubTitle: string = '';              // 横幅副标题
  bannerDesc: string = '';                  // 横幅描述
  bannerType: BannerTypeEnum = 0;           // 横幅类型
  bannerValue: string = '';                 // 横幅值
  mediaType: MediaTypeEnum = MediaTypeEnum.IMAGE; // 媒体类型
  mediaUrl: string = '';                    // 媒体URL
  detailsUrl: string = '';                  // 详情URL
  tabViewType: number = -1;                 // 标签视图类型
}

// 横幅类型枚举
export enum BannerTypeEnum {
  COMPONENT = 1,    // 组件类型
  SAMPLE = 2,       // 示例类型
  CODELAB = 3,      // 代码实验室类型
  ARTICLE = 4,      // 文章类型
  UNKNOWN = 0       // 未知类型
}

// 缩放因子常量
export const BANNER_SCALE_FACTOR: number = 3.00;  // 横幅放大因子
export const TITLE_SCALE_FACTOR: number = 6.00;   // 标题放大因子
```

**使用示例**：
```typescript
// 创建组件类型横幅
const componentBanner: BannerData = {
  id: 1001,
  bannerTitle: 'Button组件',
  bannerSubTitle: '基础交互组件',
  bannerDesc: '提供丰富的按钮样式和交互效果',
  bannerType: BannerTypeEnum.COMPONENT,
  bannerValue: 'button_component',
  mediaType: MediaTypeEnum.IMAGE,
  mediaUrl: 'images/component_button.png',
  detailsUrl: '/pages/ComponentDetailPage',
  tabViewType: TabBarType.HOME
};

// 创建文章类型横幅
const articleBanner: BannerData = {
  id: 2001,
  bannerTitle: 'HarmonyOS开发指南',
  bannerSubTitle: '技术文章',
  bannerDesc: '详细介绍HarmonyOS应用开发的最佳实践',
  bannerType: BannerTypeEnum.ARTICLE,
  bannerValue: 'development_guide',
  mediaType: MediaTypeEnum.IMAGE,
  mediaUrl: 'images/article_guide.png',
  detailsUrl: '/pages/ArticleDetailPage',
  tabViewType: TabBarType.HOME
};
```

#### CardData - 卡片数据模型
**文件位置**：`src/main/ets/model/CardData.ets`

**数据结构详解**：
```typescript
// 卡片内容类
export class CardContent {
  id: number = 0;                    // 内容唯一标识
  type: CardTypeEnum = CardTypeEnum.UNKNOWN; // 内容类型
  mediaType: MediaTypeEnum = MediaTypeEnum.IMAGE; // 媒体类型
  mediaUrl: string = '';             // 媒体URL
  title: string = '';                // 内容标题
  subTitle: string = '';             // 内容副标题
  desc: string = '';                 // 内容描述
  detailsUrl?: string = '';          // 详情URL
  originalUrl?: string = '';         // 原始URL
}

// 卡片数据类
export class CardData {
  id: number = 0;                    // 卡片唯一标识
  cardTitle: string = '';            // 卡片主标题
  cardSubTitle: string = '';         // 卡片副标题
  cardType: CardTypeEnum = CardTypeEnum.UNKNOWN; // 卡片类型
  cardStyleType: CardStyleTypeEnum = CardStyleTypeEnum.LIST; // 卡片样式
  cardImage: string = '';            // 卡片图片
  version: string = '';              // 卡片版本
  cardContents: CardContent[] = [];  // 卡片内容列表
}

// 卡片样式类型枚举
export enum CardStyleTypeEnum {
  PICTURE_ABOVE_LIST = 1,    // 图片在上方的列表样式
  LIST = 2,                  // 纯列表样式
  PICTURE = 3,               // 纯图片样式
  PICTURE_ABOVE_TEXT = 4,    // 图片在上方的文本样式
  PICTURE_TO_SWIPER = 5      // 图片转轮播样式
}

// 媒体类型枚举
export enum MediaTypeEnum {
  IMAGE = 1,     // 图片类型
  VIDEO = 2,     // 视频类型
  SYMBOL = 3     // 符号图标类型
}

// 卡片类型枚举
export enum CardTypeEnum {
  COMPONENT = 1,  // 组件类型
  SAMPLE = 2,     // 示例类型
  CODELAB = 3,    // 代码实验室类型
  ARTICLE = 4,    // 文章类型
  UNKNOWN = 0     // 未知类型
}
```

**使用示例**：
```typescript
// 创建组件卡片
const componentCard: CardData = {
  id: 1001,
  cardTitle: 'UI组件',
  cardSubTitle: '基础组件库',
  cardType: CardTypeEnum.COMPONENT,
  cardStyleType: CardStyleTypeEnum.PICTURE_ABOVE_LIST,
  cardImage: 'images/card_component.png',
  version: '1.0.0',
  cardContents: [
    {
      id: 1,
      type: CardTypeEnum.COMPONENT,
      mediaType: MediaTypeEnum.IMAGE,
      mediaUrl: 'images/button_component.png',
      title: 'Button',
      subTitle: '按钮组件',
      desc: '提供多种样式的按钮组件',
      detailsUrl: '/pages/ButtonDetailPage'
    },
    {
      id: 2,
      type: CardTypeEnum.COMPONENT,
      mediaType: MediaTypeEnum.IMAGE,
      mediaUrl: 'images/text_component.png',
      title: 'Text',
      subTitle: '文本组件',
      desc: '用于显示文本内容的组件',
      detailsUrl: '/pages/TextDetailPage'
    }
  ]
};

// 创建示例卡片
const sampleCard: CardData = {
  id: 2001,
  cardTitle: '应用示例',
  cardSubTitle: '完整应用案例',
  cardType: CardTypeEnum.SAMPLE,
  cardStyleType: CardStyleTypeEnum.PICTURE_TO_SWIPER,
  cardImage: 'images/card_sample.png',
  version: '2.0.0',
  cardContents: [
    {
      id: 1,
      type: CardTypeEnum.SAMPLE,
      mediaType: MediaTypeEnum.VIDEO,
      mediaUrl: 'videos/todo_app_demo.mp4',
      title: '待办事项应用',
      subTitle: '任务管理',
      desc: '完整的待办事项管理应用示例',
      detailsUrl: '/pages/TodoSamplePage'
    }
  ]
};
```

### 视图模型详解

#### BaseHomeViewModel - 基础首页视图模型
**文件位置**：`src/main/ets/viewmodel/BaseHomeViewModel.ets`

**核心功能**：
```typescript
@Observed
export class BaseHomeViewModel<T extends BaseHomeState> extends BaseVM<BaseHomeState> {
  protected state: T;                        // 状态对象
  protected readonly pageSize: number = 30;  // 分页大小
  private originBannerHeight: number = 0;    // 原始横幅高度
  private springBackAnimation: curves.ICurve; // 回弹动画曲线

  // 构造函数
  constructor(initialState: T) {
    super(initialState);
    this.state = initialState;
    this.initializeNavigation();
    this.calculateBannerHeight();
  }

  // 初始化导航栏
  private initializeNavigation(): void {
    const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
    const isLargeWidth = (globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ||
      globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL);

    // 设置导航栏样式
    this.state.topNavigationData.bgColor = isLargeWidth ? '#FFF1F3F5' : '#00FFFFFF';
    this.state.topNavigationData.titleColor = isLargeWidth ?
      StatusBarColorType.BLACK : StatusBarColorType.WHITE;
  }

  // 计算横幅高度
  private calculateBannerHeight(): void {
    const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
    const naviTitleHeight = globalInfoModel.statusBarHeight +
      CommonConstants.NAVIGATION_HEIGHT + CommonConstants.SPACE_8;

    if (globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ||
        globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) {
      this.state.bannerState.bannerHeight = BANNER_HEIGHT_LG;
    } else {
      this.state.bannerState.bannerHeight = globalInfoModel.deviceHeight * 0.3;
    }

    this.originBannerHeight = this.state.bannerState.bannerHeight;
  }

  // 断点变化处理
  handleBreakPointChange(): void {
    this.calculateBannerHeight();
    this.initializeNavigation();
    this.notifyPropertyChange();
  }

  // 横幅点击处理
  handleBannerItemClick(bannerData: BannerData): void {
    Logger.info(TAG, `Banner clicked: ${bannerData.bannerTitle}`);

    switch (bannerData.bannerType) {
      case BannerTypeEnum.COMPONENT:
        this.navigateToComponent(bannerData);
        break;
      case BannerTypeEnum.SAMPLE:
        this.navigateToSample(bannerData);
        break;
      case BannerTypeEnum.ARTICLE:
        this.navigateToArticle(bannerData);
        break;
      case BannerTypeEnum.CODELAB:
        this.navigateToCodelab(bannerData);
        break;
      default:
        Logger.warn(TAG, `Unknown banner type: ${bannerData.bannerType}`);
        break;
    }
  }

  // 滚动事件处理
  onScroll(xOffset: number, yOffset: number): void {
    const offsetParam: OffsetParam = { xOffset, yOffset };
    const heightParam = this.calculateHeightParam(offsetParam);

    // 更新横幅高度和缩放
    this.state.bannerState.bannerHeight = heightParam.bannerHeight;
    this.state.bannerState.bannerScale = heightParam.bannerScale;
    this.state.topNavigationData.titleScale = heightParam.titleScale;

    this.notifyPropertyChange();
  }

  // 计算高度参数
  calculateHeightParam(offsetParam: OffsetParam): CalculateHeightParam {
    const { yOffset } = offsetParam;
    let bannerHeight = this.originBannerHeight;
    let bannerScale = 1.0;
    let titleScale = 1.0;

    if (yOffset < 0) {
      // 下拉时放大横幅
      const scaleFactor = Math.abs(yOffset) / BANNER_SCALE_FACTOR;
      bannerHeight = this.originBannerHeight + Math.abs(yOffset);
      bannerScale = 1.0 + scaleFactor;
    } else if (yOffset > 0) {
      // 上滑时缩放标题
      const titleScaleFactor = yOffset / TITLE_SCALE_FACTOR;
      titleScale = Math.max(TITLE_MIN_SCALE,
        Math.min(TITLE_MAX_SCALE, 1.0 + titleScaleFactor * TITLE_OFFSET_FACTOR));
    }

    return {
      bannerHeight,
      bannerScale,
      titleScale
    };
  }

  // 页面显示生命周期
  onPageShow(): void {
    Logger.info(TAG, 'Page show');
    this.updateStatusBar();
  }

  // 页面隐藏生命周期
  onPageHide(): void {
    Logger.info(TAG, 'Page hide');
  }

  // 更新状态栏
  private updateStatusBar(): void {
    const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
    const tabViewType = this.state.topNavigationData.tabViewType || 0;
    const isDarkStatusBar = TAB_CONTENT_STATUSES[tabViewType] || false;

    WindowUtil.setStatusBarColor(isDarkStatusBar ?
      StatusBarColorType.BLACK : StatusBarColorType.WHITE);
  }

  // 导航到组件详情
  private navigateToComponent(bannerData: BannerData): void {
    const params: ComponentDetailParams = {
      componentName: bannerData.bannerTitle,
      componentId: bannerData.id
    };

    router.pushUrl({
      url: 'pages/ComponentDetailPage',
      params: params
    }).catch((error: Error) => {
      Logger.error(TAG, 'Navigate to component failed', error);
    });
  }

  // 导航到示例详情
  private navigateToSample(bannerData: BannerData): void {
    const params: SampleDetailParams = {
      currentIndex: 0,
      sampleCardId: bannerData.id
    };

    router.pushUrl({
      url: 'pages/SampleDetailPage',
      params: params
    }).catch((error: Error) => {
      Logger.error(TAG, 'Navigate to sample failed', error);
    });
  }

  // 导航到文章详情
  private navigateToArticle(bannerData: BannerData): void {
    const params: ArticleDetailParams = {
      id: bannerData.id,
      isArticle: true,
      title: bannerData.bannerTitle,
      detailsUrl: bannerData.detailsUrl,
      tabViewType: bannerData.tabViewType
    };

    router.pushUrl({
      url: 'pages/ArticleDetailPage',
      params: params
    }).catch((error: Error) => {
      Logger.error(TAG, 'Navigate to article failed', error);
    });
  }

  // 导航到代码实验室
  private navigateToCodelab(bannerData: BannerData): void {
    // 打开外部链接或内部页面
    if (bannerData.detailsUrl.startsWith('http')) {
      // 打开外部浏览器
      this.openExternalUrl(bannerData.detailsUrl);
    } else {
      // 内部页面导航
      router.pushUrl({
        url: bannerData.detailsUrl,
        params: { bannerData }
      }).catch((error: Error) => {
        Logger.error(TAG, 'Navigate to codelab failed', error);
      });
    }
  }

  // 打开外部URL
  private openExternalUrl(url: string): void {
    // 实现打开外部浏览器的逻辑
    Logger.info(TAG, `Opening external URL: ${url}`);
  }
}

// 事件类型枚举
export enum BaseHomeEventType {
  BANNER_CLICKED = 'bannerClicked',
  SCROLL_CHANGED = 'scrollChanged',
  BREAKPOINT_CHANGED = 'breakpointChanged',
  PAGE_SHOW = 'pageShow',
  PAGE_HIDE = 'pageHide'
}

// 事件参数接口
export interface BaseHomeEventParam {
  type: BaseHomeEventType;
  data?: any;
}

// 计算高度参数接口
export interface CalculateHeightParam {
  bannerHeight: number;
  bannerScale: number;
  titleScale: number;
}

// 偏移参数接口
export interface OffsetParam {
  xOffset: number;
  yOffset: number;
}
```

#### BannerSource - 横幅数据源
**文件位置**：`src/main/ets/viewmodel/BannerSource.ets`

**数据源管理**：
```typescript
export class BannerSource extends BasicDataSource {
  private bannerDataArray: BannerData[] = [];

  constructor(bannerDataArray: BannerData[]) {
    super();
    this.bannerDataArray = bannerDataArray;
  }

  // 获取数据总数
  totalCount(): number {
    return this.bannerDataArray.length;
  }

  // 获取指定位置的数据
  getData(index: number): BannerData {
    if (index >= 0 && index < this.bannerDataArray.length) {
      return this.bannerDataArray[index];
    }
    return new BannerData();
  }

  // 添加数据
  addData(index: number, data: BannerData): void {
    this.bannerDataArray.splice(index, 0, data);
    this.notifyDataAdd(index);
  }

  // 删除数据
  removeData(index: number): void {
    if (index >= 0 && index < this.bannerDataArray.length) {
      this.bannerDataArray.splice(index, 1);
      this.notifyDataDelete(index);
    }
  }

  // 更新数据
  updateData(index: number, data: BannerData): void {
    if (index >= 0 && index < this.bannerDataArray.length) {
      this.bannerDataArray[index] = data;
      this.notifyDataChange(index);
    }
  }

  // 批量更新数据
  updateAllData(newDataArray: BannerData[]): void {
    this.bannerDataArray = [...newDataArray];
    this.notifyDataReload();
  }

  // 清空数据
  clearData(): void {
    const count = this.bannerDataArray.length;
    this.bannerDataArray = [];
    for (let i = count - 1; i >= 0; i--) {
      this.notifyDataDelete(i);
    }
  }

  // 获取所有数据
  getAllData(): BannerData[] {
    return [...this.bannerDataArray];
  }

  // 查找数据
  findData(predicate: (data: BannerData) => boolean): BannerData | undefined {
    return this.bannerDataArray.find(predicate);
  }

  // 过滤数据
  filterData(predicate: (data: BannerData) => boolean): BannerData[] {
    return this.bannerDataArray.filter(predicate);
  }
}
```

**使用示例**：
```typescript
// 创建横幅数据数组
const bannerDataArray: BannerData[] = [
  {
    id: 1,
    bannerTitle: 'Button组件',
    bannerType: BannerTypeEnum.COMPONENT,
    // ... 其他属性
  },
  {
    id: 2,
    bannerTitle: '应用示例',
    bannerType: BannerTypeEnum.SAMPLE,
    // ... 其他属性
  }
];

// 创建横幅数据源
const bannerSource = new BannerSource(bannerDataArray);

// 动态添加数据
const newBanner: BannerData = {
  id: 3,
  bannerTitle: '新文章',
  bannerType: BannerTypeEnum.ARTICLE,
  // ... 其他属性
};
bannerSource.addData(bannerSource.totalCount(), newBanner);

// 查找特定类型的横幅
const componentBanners = bannerSource.filterData(
  (banner) => banner.bannerType === BannerTypeEnum.COMPONENT
);

// 更新横幅数据
const updatedBanner = { ...newBanner, bannerTitle: '更新后的文章' };
bannerSource.updateData(2, updatedBanner);
```

## 架构模式

### MVVM架构实现
CommonBusiness模块严格遵循MVVM（Model-View-ViewModel）架构模式：

**Model层**：
- BannerData、CardData等数据模型
- 定义数据结构和业务实体
- 提供数据验证和转换方法

**View层**：
- BannerCard、FullScreenNavigation等UI组件
- BaseHomeView基础视图组件
- 负责UI渲染和用户交互

**ViewModel层**：
- BaseHomeViewModel基础视图模型
- BannerSource数据源管理
- 处理业务逻辑和状态管理

### 状态管理模式
```typescript
// 状态管理示例
class MyHomeViewModel extends BaseHomeViewModel<MyHomeState> {
  // 状态更新
  updateBannerData(newBannerData: BannerData[]): void {
    this.state.bannerState.bannerResource.updateAllData(newBannerData);
    this.notifyPropertyChange();
  }

  // 异步数据加载
  async loadBannerData(): Promise<void> {
    try {
      this.state.loadingModel.loadingStatus = LoadingStatus.LOADING;

      const bannerData = await this.bannerService.getBannerData();
      this.updateBannerData(bannerData);

      this.state.loadingModel.loadingStatus = LoadingStatus.SUCCESS;
    } catch (error) {
      this.state.loadingModel.loadingStatus = LoadingStatus.FAILED;
      Logger.error(TAG, 'Load banner data failed', error);
    }
  }

  // 事件处理
  handleUserAction(action: UserAction): void {
    switch (action.type) {
      case 'REFRESH':
        this.loadBannerData();
        break;
      case 'BANNER_CLICK':
        this.handleBannerItemClick(action.data);
        break;
      default:
        break;
    }
  }
}
```

## 性能优化

### 1. 懒加载和虚拟化
```typescript
// 横幅卡片懒加载
LazyForEach(this.bannerSource, (bannerData: BannerData) => {
  BannerItem({
    bannerData: bannerData,
    onItemClick: this.handleBannerClick
  })
}, (bannerData: BannerData) => bannerData.id.toString())
```

### 2. 图片优化
```typescript
// 图片预加载和缓存
class ImageOptimizer {
  private imageCache: Map<string, PixelMap> = new Map();

  async preloadImage(url: string): Promise<void> {
    if (!this.imageCache.has(url)) {
      try {
        const pixelMap = await image.createImageSource(url).createPixelMap();
        this.imageCache.set(url, pixelMap);
      } catch (error) {
        Logger.error('ImageOptimizer', 'Preload image failed', error);
      }
    }
  }

  getCachedImage(url: string): PixelMap | undefined {
    return this.imageCache.get(url);
  }
}
```

### 3. 动画优化
```typescript
// 使用硬件加速的动画
@Component
struct OptimizedBannerCard {
  @State bannerScale: number = 1.0;

  build() {
    Image(this.bannerData.mediaUrl)
      .scale({ x: this.bannerScale, y: this.bannerScale })
      .animation({
        duration: 300,
        curve: Curve.EaseInOut,
        playMode: PlayMode.Normal
      })
      .renderGroup(true) // 启用渲染组优化
  }
}
```

## 测试策略

### 单元测试
```typescript
// 数据模型测试
describe('BannerData', () => {
  it('should create banner data with default values', () => {
    const banner = new BannerData();
    expect(banner.id).toBe(0);
    expect(banner.bannerTitle).toBe('');
    expect(banner.bannerType).toBe(BannerTypeEnum.UNKNOWN);
  });

  it('should validate banner data', () => {
    const banner = new BannerData();
    banner.id = 1;
    banner.bannerTitle = 'Test Banner';
    banner.bannerType = BannerTypeEnum.COMPONENT;

    expect(banner.isValid()).toBe(true);
  });
});

// 视图模型测试
describe('BaseHomeViewModel', () => {
  let viewModel: BaseHomeViewModel<BaseHomeState>;

  beforeEach(() => {
    viewModel = new BaseHomeViewModel(new BaseHomeState());
  });

  it('should handle banner click correctly', () => {
    const bannerData: BannerData = {
      id: 1,
      bannerType: BannerTypeEnum.COMPONENT,
      bannerTitle: 'Test Component'
    };

    const navigateSpy = jest.spyOn(router, 'pushUrl');
    viewModel.handleBannerItemClick(bannerData);

    expect(navigateSpy).toHaveBeenCalledWith({
      url: 'pages/ComponentDetailPage',
      params: expect.objectContaining({
        componentId: 1,
        componentName: 'Test Component'
      })
    });
  });
});
```

### 集成测试
```typescript
// 组件集成测试
describe('BannerCard Integration', () => {
  it('should render banner items correctly', async () => {
    const bannerData: BannerData[] = [
      { id: 1, bannerTitle: 'Banner 1', bannerType: BannerTypeEnum.COMPONENT },
      { id: 2, bannerTitle: 'Banner 2', bannerType: BannerTypeEnum.SAMPLE }
    ];

    const bannerSource = new BannerSource(bannerData);
    const bannerState: BannerState = {
      bannerResource: bannerSource,
      bannerHeight: 200,
      bannerScale: 1.0
    };

    // 渲染组件
    const component = render(BannerCard({
      bannerState: bannerState,
      tabViewType: TabBarType.HOME
    }));

    // 验证渲染结果
    expect(component.findByText('Banner 1')).toBeTruthy();
    expect(component.findByText('Banner 2')).toBeTruthy();
  });
});
```

## 常见问题解决

### 1. 横幅图片加载失败
**原因**：网络问题或图片URL无效
**解决方案**：
```typescript
// 添加图片加载错误处理
Image(this.bannerData.mediaUrl)
  .alt($r('app.media.ic_banner_placeholder')) // 设置占位图
  .onError(() => {
    Logger.warn(TAG, `Banner image load failed: ${this.bannerData.mediaUrl}`);
    // 可以尝试重新加载或使用默认图片
    this.loadDefaultImage();
  })
  .onComplete(() => {
    Logger.info(TAG, `Banner image loaded: ${this.bannerData.mediaUrl}`);
  })
```

### 2. 滚动性能问题
**原因**：频繁的状态更新导致性能下降
**解决方案**：
```typescript
// 使用防抖处理滚动事件
class ScrollHandler {
  private scrollTimer: number | null = null;
  private readonly SCROLL_DEBOUNCE_TIME = 16; // 约60fps

  onScroll(xOffset: number, yOffset: number): void {
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
    }

    this.scrollTimer = setTimeout(() => {
      this.handleScrollUpdate(xOffset, yOffset);
      this.scrollTimer = null;
    }, this.SCROLL_DEBOUNCE_TIME);
  }

  private handleScrollUpdate(xOffset: number, yOffset: number): void {
    // 实际的滚动处理逻辑
  }
}
```

### 3. 内存泄漏问题
**原因**：事件监听器未正确清理
**解决方案**：
```typescript
// 正确的生命周期管理
@Component
struct BannerCard {
  private viewModel: BaseHomeViewModel<BaseHomeState> | null = null;
  private eventListeners: Function[] = [];

  aboutToAppear(): void {
    this.viewModel = new BaseHomeViewModel(new BaseHomeState());

    // 添加事件监听器
    const scrollListener = (event: ScrollEvent) => this.handleScroll(event);
    this.eventListeners.push(scrollListener);
  }

  aboutToDisappear(): void {
    // 清理事件监听器
    this.eventListeners.forEach(listener => {
      // 移除事件监听器
    });
    this.eventListeners = [];

    // 清理视图模型
    this.viewModel?.dispose();
    this.viewModel = null;
  }
}
```

## 最佳实践

### 1. 组件设计原则
- **单一职责**：每个组件只负责一个特定功能
- **可复用性**：设计通用的组件接口和属性
- **响应式设计**：支持不同屏幕尺寸和断点
- **性能优化**：使用懒加载和虚拟化技术

### 2. 数据管理原则
- **不可变性**：避免直接修改状态对象
- **类型安全**：使用TypeScript类型定义
- **数据验证**：在数据模型中添加验证逻辑
- **缓存策略**：合理使用内存和磁盘缓存

### 3. 代码组织原则
- **模块化**：按功能划分模块和文件
- **命名规范**：使用清晰的命名约定
- **文档完善**：提供详细的代码注释和文档
- **测试覆盖**：编写全面的单元测试和集成测试

## 版本更新说明

### v1.0.0 (当前版本)
- 基础业务组件库
- MVVM架构支持
- 响应式布局组件
- 横幅和卡片展示功能
- 基础导航和详情组件

### 后续版本规划
- 更多业务组件类型
- 动画效果增强
- 性能优化改进
- 国际化支持扩展
- 主题定制功能
- 无障碍访问支持

### 3. 性能优化最佳实践

**LazyForEach使用**：
```typescript
// ✅ 推荐做法 - 使用LazyForEach优化长列表
@Component
struct OptimizedBannerCard {
  @State bannerState: BannerState = new BannerState();

  build() {
    Swiper() {
      LazyForEach(this.bannerState.bannerResource, (bannerData: BannerData, index: number) => {
        BannerItem({
          bannerData: bannerData,
          onItemClick: this.handleItemClick
        })
      }, (bannerData: BannerData) => bannerData.id.toString())
    }
    .cachedCount(3) // 缓存3个页面
    .autoPlay(true)
    .interval(4000)
  }

  private handleItemClick = (bannerData: BannerData): void => {
    // 使用箭头函数避免this绑定问题
    this.navigationService.navigate(bannerData);
  }
}

// ❌ 不推荐做法 - 直接使用ForEach
@Component
struct UnoptimizedBannerCard {
  @State bannerList: BannerData[] = [];

  build() {
    Swiper() {
      ForEach(this.bannerList, (bannerData: BannerData) => {
        BannerItem({
          bannerData: bannerData,
          onItemClick: (data: BannerData) => {
            // 每次渲染都会创建新的函数，影响性能
            router.pushUrl({ url: 'pages/Detail', params: data });
          }
        })
      })
    }
  }
}
```

**状态更新优化**：
```typescript
// ✅ 推荐做法 - 批量更新状态
class OptimizedHomeViewModel extends BaseHomeViewModel<MyHomeState> {
  public async loadPageData(): Promise<void> {
    try {
      // 并行加载数据
      const [bannerData, cardData, tabData] = await Promise.all([
        this.bannerService.getBannerList(),
        this.cardService.getCardList(),
        this.tabService.getTabList()
      ]);

      // 批量更新状态，减少重渲染次数
      this.updateState({
        bannerState: { bannerResource: new BannerSource(bannerData) },
        cardList: cardData,
        tabList: tabData,
        isLoading: false
      });

    } catch (error) {
      this.updateState({
        isLoading: false,
        errorMessage: error.message
      });
    }
  }
}

// ❌ 不推荐做法 - 分别更新状态
class UnoptimizedHomeViewModel extends BaseHomeViewModel<MyHomeState> {
  public async loadPageData(): Promise<void> {
    try {
      // 串行加载，效率低
      const bannerData = await this.bannerService.getBannerList();
      this.updateState({ bannerState: { bannerResource: new BannerSource(bannerData) } });

      const cardData = await this.cardService.getCardList();
      this.updateState({ cardList: cardData });

      const tabData = await this.tabService.getTabList();
      this.updateState({ tabList: tabData });

      this.updateState({ isLoading: false });

    } catch (error) {
      this.updateState({ isLoading: false, errorMessage: error.message });
    }
  }
}
```

### 4. 内存管理最佳实践

**正确的生命周期管理**：
```typescript
// ✅ 推荐做法
@Component
struct HomePage {
  private viewModel: MyHomeViewModel = new MyHomeViewModel();
  private timerId: number = -1;

  aboutToAppear(): void {
    // 添加监听器
    this.viewModel.addStateListener(this.onStateChange);

    // 启动定时器
    this.timerId = setInterval(() => {
      this.viewModel.refreshData();
    }, 30000);

    // 加载数据
    this.viewModel.loadData();
  }

  aboutToDisappear(): void {
    // 清理监听器
    this.viewModel.removeStateListener(this.onStateChange);

    // 清理定时器
    if (this.timerId !== -1) {
      clearInterval(this.timerId);
      this.timerId = -1;
    }

    // 销毁视图模型
    this.viewModel.onDestroy();
  }

  private onStateChange = (newState: MyHomeState): void => {
    // 状态变化处理
  }
}

// ❌ 不推荐做法
@Component
struct HomePage {
  private viewModel: MyHomeViewModel = new MyHomeViewModel();

  aboutToAppear(): void {
    this.viewModel.addStateListener((newState: MyHomeState) => {
      // 匿名函数无法在aboutToDisappear中移除
    });

    setInterval(() => {
      this.viewModel.refreshData();
    }, 30000); // 定时器没有清理，会导致内存泄漏
  }

  // 缺少aboutToDisappear，没有清理资源
}
```

## 扩展开发指南

### 1. 添加新的横幅类型

```typescript
// 1. 扩展BannerTypeEnum
export enum BannerTypeEnum {
  COMPONENT = 1,
  SAMPLE = 2,
  CODELAB = 3,
  ARTICLE = 4,
  VIDEO = 5,      // 新增视频类型
  COURSE = 6,     // 新增课程类型
  UNKNOWN = 0,
}

// 2. 在BaseHomeViewModel中添加处理逻辑
class ExtendedHomeViewModel extends BaseHomeViewModel<MyHomeState> {
  public handleBannerClick(bannerData: BannerData): void {
    switch (bannerData.bannerType) {
      case BannerTypeEnum.VIDEO:
        this.navigateToVideoDetail(bannerData);
        break;
      case BannerTypeEnum.COURSE:
        this.navigateToCourseDetail(bannerData);
        break;
      default:
        super.handleBannerClick(bannerData);
        break;
    }
  }

  private navigateToVideoDetail(bannerData: BannerData): void {
    // 视频详情页导航逻辑
  }

  private navigateToCourseDetail(bannerData: BannerData): void {
    // 课程详情页导航逻辑
  }
}
```

### 2. 自定义卡片样式

```typescript
// 1. 扩展CardStyleTypeEnum
export enum CardStyleTypeEnum {
  LIST = 1,
  GRID = 2,
  WATERFALL = 3,  // 新增瀑布流样式
  CAROUSEL = 4,   // 新增轮播样式
}

// 2. 创建自定义卡片组件
@Component
export struct CustomCardComponent {
  @Prop cardData: CardData;
  @Prop styleType: CardStyleTypeEnum;

  build() {
    if (this.styleType === CardStyleTypeEnum.WATERFALL) {
      this.buildWaterfallCard()
    } else if (this.styleType === CardStyleTypeEnum.CAROUSEL) {
      this.buildCarouselCard()
    } else {
      this.buildDefaultCard()
    }
  }

  @Builder
  buildWaterfallCard() {
    // 瀑布流卡片布局
  }

  @Builder
  buildCarouselCard() {
    // 轮播卡片布局
  }

  @Builder
  buildDefaultCard() {
    // 默认卡片布局
  }
}
```

### 3. 创建自定义业务组件

```typescript
// 1. 在component目录下创建新组件
@Component
export struct CustomBusinessComponent {
  @Prop @Require data: CustomData;
  @State private internalState: boolean = false;

  build() {
    Column() {
      // 组件内容
    }
    .padding(CommonConstants.SPACE_16)
  }
}

// 2. 定义组件数据接口
export interface CustomData {
  id: number;
  title: string;
  content: string;
}

// 3. 在Index.ets中导出
export { CustomBusinessComponent, CustomData } from './src/main/ets/component/CustomBusinessComponent';
```

## 版本兼容性

### 支持的HarmonyOS版本
- HarmonyOS 4.0+
- API Level 10+

### 依赖的系统能力
- SystemCapability.ArkUI.ArkUI.Full
- SystemCapability.Ability.AbilityRuntime.Core
- SystemCapability.Utils.Lang
- SystemCapability.Multimedia.Image.Core

### 升级指南

当升级CommonBusiness模块版本时：

1. **检查API变更**：
   - 查看CHANGELOG.md了解破坏性变更
   - 检查组件接口是否有变化
   - 验证数据模型结构是否兼容

2. **更新依赖**：
   - 确保@ohos/common模块版本兼容
   - 更新相关的工具包版本

3. **测试验证**：
   - 运行现有的单元测试
   - 验证UI组件的显示效果
   - 测试响应式布局在不同设备上的表现

4. **迁移指南**：
   - 如有破坏性变更，按照迁移文档更新代码
   - 更新组件的使用方式
   - 调整数据模型的结构

## 常见问题解决

### 1. 横幅卡片不显示问题

**问题现象**：BannerCard组件渲染但不显示内容

**可能原因**：
- BannerState数据为空
- LazyForEach数据源未正确初始化
- 图片资源加载失败

**解决方案**：
```typescript
// 检查数据源
@Component
struct DebugBannerCard {
  @State bannerState: BannerState = new BannerState();

  aboutToAppear(): void {
    this.loadAndValidateBannerData();
  }

  private async loadAndValidateBannerData(): Promise<void> {
    try {
      const bannerList = await this.bannerService.getBannerList();

      // 验证数据
      if (!bannerList || bannerList.length === 0) {
        Logger.warn('BannerCard', 'Banner list is empty');
        return;
      }

      // 验证每个横幅数据
      const validBanners = bannerList.filter(banner => {
        if (!banner.mediaUrl) {
          Logger.warn('BannerCard', `Banner ${banner.id} has no media URL`);
          return false;
        }
        return true;
      });

      this.bannerState.bannerResource.setData(validBanners);
      Logger.info('BannerCard', `Loaded ${validBanners.length} valid banners`);

    } catch (error) {
      Logger.error('BannerCard', `Load banner failed: ${error.message}`);
    }
  }

  build() {
    Column() {
      // 添加调试信息
      Text(`Banner count: ${this.bannerState.bannerResource.totalCount()}`)
        .fontSize(12)
        .fontColor(Color.Gray)
        .margin({ bottom: 8 })

      if (this.bannerState.bannerResource.totalCount() > 0) {
        BannerCard({
          bannerState: this.bannerState,
          tabViewType: 1
        })
      } else {
        Text('暂无横幅数据')
          .fontSize(16)
          .fontColor(Color.Gray)
          .textAlign(TextAlign.Center)
          .width('100%')
          .height(200)
      }
    }
  }
}
```

### 2. 状态更新不生效问题

**问题现象**：调用updateState后UI没有更新

**可能原因**：
- 状态对象没有使用@Observed装饰器
- 组件没有正确监听状态变化
- 状态更新的属性路径不正确

**解决方案**：
```typescript
// 确保状态类使用@Observed装饰器
@Observed
export class MyHomeState extends BaseHomeState {
  public customData: string = '';

  // 确保有正确的更新方法
  public updateCustomData(data: string): void {
    this.customData = data;
    this.updateTimestamp(); // 触发观察者更新
  }
}

// 确保组件正确监听状态
@Component
struct HomePage {
  private viewModel: MyHomeViewModel = new MyHomeViewModel();
  @State homeState: MyHomeState = this.viewModel.getState();

  aboutToAppear(): void {
    // 添加状态监听器
    this.viewModel.addStateListener((newState: MyHomeState) => {
      this.homeState = newState; // 重新赋值触发UI更新
    });
  }

  build() {
    Column() {
      Text(this.homeState.customData)
        .fontSize(16)
    }
  }
}

// 视图模型中正确更新状态
class MyHomeViewModel extends BaseHomeViewModel<MyHomeState> {
  public updateCustomData(data: string): void {
    // 使用updateState方法，会自动通知监听器
    this.updateState({ customData: data });

    // 或者直接更新状态对象
    this.state.updateCustomData(data);
  }
}
```

### 3. 响应式布局问题

**问题现象**：组件在不同设备上显示异常

**可能原因**：
- 断点监听没有正确设置
- BreakpointType配置不完整
- 全局信息模型未正确初始化

**解决方案**：
```typescript
// 确保全局信息模型正确初始化
@Entry
@Component
struct MainPage {
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel =
    AppStorage.get('GlobalInfoModel')!;

  aboutToAppear(): void {
    // 确保全局信息模型已初始化
    if (!this.globalInfoModel) {
      const globalInfo = new GlobalInfoModel();
      AppStorage.setOrCreate('GlobalInfoModel', globalInfo);
      this.globalInfoModel = globalInfo;
    }

    // 初始化断点系统
    this.initBreakpointSystem();
  }

  private initBreakpointSystem(): void {
    const breakpointSystem = new BreakpointSystem();
    breakpointSystem.register((breakpoint: BreakpointTypeEnum) => {
      this.globalInfoModel.currentBreakpoint = breakpoint;
    });
  }
}

// 组件中正确使用断点
@Component
struct ResponsiveComponent {
  @StorageProp('GlobalInfoModel') @Watch('onBreakpointChange')
  globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;

  @State private componentWidth: number = 0;

  onBreakpointChange(): void {
    // 根据断点更新组件宽度
    this.componentWidth = new BreakpointType({
      sm: 300,
      md: 400,
      lg: 500
    }).getValue(this.globalInfoModel.currentBreakpoint);
  }

  build() {
    Column() {
      // 使用计算出的宽度
    }
    .width(this.componentWidth)
  }
}
```

### 4. 内存泄漏问题

**问题现象**：应用长时间运行后内存持续增长

**可能原因**：
- 事件监听器没有正确移除
- 定时器没有清理
- 循环引用导致对象无法回收

**解决方案**：
```typescript
// 正确的资源管理
class ResourceManager {
  private listeners: Array<() => void> = [];
  private timers: number[] = [];
  private subscriptions: Array<{ unsubscribe: () => void }> = [];

  public addListener(listener: () => void): void {
    this.listeners.push(listener);
  }

  public addTimer(timerId: number): void {
    this.timers.push(timerId);
  }

  public addSubscription(subscription: { unsubscribe: () => void }): void {
    this.subscriptions.push(subscription);
  }

  public cleanup(): void {
    // 清理监听器
    this.listeners.forEach(listener => {
      // 移除监听器的具体实现
    });
    this.listeners = [];

    // 清理定时器
    this.timers.forEach(timerId => {
      clearInterval(timerId);
    });
    this.timers = [];

    // 清理订阅
    this.subscriptions.forEach(subscription => {
      subscription.unsubscribe();
    });
    this.subscriptions = [];
  }
}

// 在组件中使用资源管理器
@Component
struct ManagedComponent {
  private resourceManager: ResourceManager = new ResourceManager();

  aboutToAppear(): void {
    // 添加监听器时注册到资源管理器
    const listener = () => { /* 处理逻辑 */ };
    this.someService.addListener(listener);
    this.resourceManager.addListener(() => {
      this.someService.removeListener(listener);
    });

    // 添加定时器时注册到资源管理器
    const timerId = setInterval(() => { /* 定时任务 */ }, 1000);
    this.resourceManager.addTimer(timerId);
  }

  aboutToDisappear(): void {
    // 统一清理所有资源
    this.resourceManager.cleanup();
  }
}
```

## 测试指南

### 1. 单元测试示例

```typescript
// 数据模型测试
describe('BannerData', () => {
  it('should create banner data with default values', () => {
    const banner = new BannerData();
    expect(banner.id).toBe(0);
    expect(banner.bannerTitle).toBe('');
    expect(banner.bannerType).toBe(BannerTypeEnum.UNKNOWN);
  });

  it('should validate banner data', () => {
    const banner = new BannerData();
    banner.id = 1;
    banner.bannerTitle = 'Test Banner';
    banner.bannerType = BannerTypeEnum.COMPONENT;

    expect(banner.isValid()).toBe(true);
  });
});

// 视图模型测试
describe('BaseHomeViewModel', () => {
  let viewModel: BaseHomeViewModel<BaseHomeState>;

  beforeEach(() => {
    viewModel = new BaseHomeViewModel(new BaseHomeState());
  });

  it('should handle banner click correctly', () => {
    const bannerData: BannerData = {
      id: 1,
      bannerType: BannerTypeEnum.COMPONENT,
      bannerTitle: 'Test Component'
    };

    const navigateSpy = jest.spyOn(router, 'pushUrl');
    viewModel.handleBannerItemClick(bannerData);

    expect(navigateSpy).toHaveBeenCalledWith({
      url: 'pages/ComponentDetailPage',
      params: expect.objectContaining({
        componentId: 1,
        componentName: 'Test Component'
      })
    });
  });
});
```

### 2. 集成测试示例

```typescript
// 组件集成测试
describe('BannerCard Integration', () => {
  it('should render banner items correctly', async () => {
    const bannerData: BannerData[] = [
      { id: 1, bannerTitle: 'Banner 1', bannerType: BannerTypeEnum.COMPONENT },
      { id: 2, bannerTitle: 'Banner 2', bannerType: BannerTypeEnum.SAMPLE }
    ];

    const bannerSource = new BannerSource(bannerData);
    const bannerState: BannerState = {
      bannerResource: bannerSource,
      bannerHeight: 200,
      bannerScale: 1.0
    };

    // 渲染组件
    const component = render(BannerCard({
      bannerState: bannerState,
      tabViewType: TabBarType.HOME
    }));

    // 验证渲染结果
    expect(component.findByText('Banner 1')).toBeTruthy();
    expect(component.findByText('Banner 2')).toBeTruthy();
  });
});
```

## 贡献指南

### 1. 代码规范

- 使用TypeScript进行类型安全开发
- 遵循ArkTS编码规范
- 使用ESLint进行代码检查
- 编写详细的代码注释

### 2. 提交规范

- 使用语义化的提交信息
- 每个提交只包含一个功能或修复
- 提供详细的变更说明
- 更新相关的文档和测试

### 3. 测试要求

- 新功能必须包含单元测试
- 测试覆盖率不低于80%
- 通过所有现有测试
- 提供集成测试示例

---

**注意**：本文档会随着模块的更新而持续维护，请关注版本变更和最新的使用指南。如有问题或建议，请通过项目的Issue系统反馈。
