# Almighty - HarmonyOS全能应用项目

## 项目概述
Almighty是一个基于HarmonyOS开发的全能应用项目，采用模块化架构设计，包含首页、安全、社区、个人中心等多个功能模块。项目使用ArkTS语言开发，支持手机、平板、2in1设备等多种设备形态，实现了完整的响应式设计和现代化的用户界面。

## 项目信息
- **应用包名**：com.sucorde.almighty
- **版本号**：1.0.0 (versionCode: 1000000)
- **开发语言**：ArkTS (TypeScript for HarmonyOS)
- **目标平台**：HarmonyOS 4.0+
- **支持设备**：手机、平板、2in1设备

## 项目架构

### 整体架构图
```
Almighty Project
├── AppScope/                    # 应用级配置
├── Common/                      # 通用基础模块
├── Features/                    # 功能特性模块
│   ├── Home/                   # 首页模块
│   ├── Security/               # 安全模块
│   ├── Community/              # 社区模块
│   ├── Mine/                   # 个人中心模块
│   └── CommonBusiness/         # 通用业务模块
└── Multiverse/                 # 多设备适配
    └── Phone/                  # 手机应用主模块
```

### 模块依赖关系

#### 依赖层次结构
```mermaid
graph TD
    subgraph "应用层 (Entry Module)"
        A[Phone主模块<br/>@ohos/phone<br/>EntryAbility<br/>MainPage<br/>SplashPage]
    end

    subgraph "功能层 (Feature Modules)"
        B[Home首页<br/>@ohos/home<br/>ComponentListView<br/>ComponentListModel]
        C[Security安全<br/>@ohos/security<br/>PracticesView<br/>SampleModel]
        D[Community社区<br/>@ohos/community<br/>ExplorationView<br/>DiscoverModel]
        E[Mine个人中心<br/>@ohos/mine<br/>MineView<br/>AboutView]
    end

    subgraph "业务层 (Business Module)"
        F[CommonBusiness通用业务<br/>@ohos/commonbusiness<br/>BaseHomeView<br/>BannerCard<br/>TabBarType]
    end

    subgraph "基础层 (Foundation Module)"
        G[Common通用基础<br/>@ohos/common<br/>BaseButton<br/>TopNavigationView<br/>Logger<br/>WindowUtil]
    end

    subgraph "系统层 (System APIs)"
        H1[@kit.ArkUI<br/>UIAbility<br/>window<br/>Configuration]
        H2[@kit.AbilityKit<br/>Want<br/>AbilityConstant<br/>common]
        H3[@kit.BasicServicesKit<br/>BusinessError<br/>preferences]
        H4[@kit.PerformanceAnalysisKit<br/>hiTraceMeter<br/>hilog]
    end

    %% 应用层依赖
    A -->|导入视图组件| B
    A -->|导入视图组件| C
    A -->|导入视图组件| D
    A -->|导入视图组件| E
    A -->|导入业务组件| F
    A -->|导入基础工具| G

    %% 功能层依赖
    B -->|使用业务组件| F
    B -->|使用基础组件| G
    C -->|使用业务组件| F
    C -->|使用基础组件| G
    D -->|使用业务组件| F
    D -->|使用基础组件| G
    E -->|仅使用基础组件| G

    %% 业务层依赖
    F -->|继承基础类| G

    %% 基础层依赖
    G -->|UI框架| H1
    G -->|能力框架| H2
    G -->|基础服务| H3
    G -->|性能分析| H4

    %% 功能层直接系统依赖
    F -->|UI组件| H1
    B -->|页面能力| H1
    C -->|页面能力| H1
    D -->|页面能力| H1
    E -->|页面能力| H1
    A -->|应用能力| H1
    A -->|生命周期| H2

    %% 样式定义
    classDef entryModule fill:#ff9999,stroke:#333,stroke-width:3px
    classDef featureModule fill:#99ccff,stroke:#333,stroke-width:2px
    classDef businessModule fill:#99ff99,stroke:#333,stroke-width:2px
    classDef foundationModule fill:#ffcc99,stroke:#333,stroke-width:2px
    classDef systemModule fill:#cccccc,stroke:#333,stroke-width:1px

    class A entryModule
    class B,C,D,E featureModule
    class F businessModule
    class G foundationModule
    class H1,H2,H3,H4 systemModule
```

#### 详细依赖关系图（按功能分类）
```mermaid
graph LR
    subgraph "Phone主模块核心组件"
        A1[EntryAbility<br/>应用入口]
        A2[MainPage<br/>主页面]
        A3[SplashPage<br/>启动页]
        A4[CustomTabBar<br/>标签栏]
        A5[CustomSideBar<br/>侧边栏]
    end

    subgraph "Home模块组件"
        B1[ComponentListView<br/>组件列表视图]
        B2[ComponentListModel<br/>组件数据模型]
        B3[ComponentListViewModel<br/>组件视图模型]
    end

    subgraph "Security模块组件"
        C1[PracticesView<br/>实践视图]
        C2[SampleModel<br/>示例数据模型]
        C3[SampleViewModel<br/>示例视图模型]
    end

    subgraph "Community模块组件"
        D1[ExplorationView<br/>探索视图]
        D2[DiscoverModel<br/>发现数据模型]
        D3[DiscoverViewModel<br/>发现视图模型]
    end

    subgraph "Mine模块组件"
        E1[MineView<br/>个人中心视图]
        E2[AboutView<br/>关于页面]
        E3[MineViewModel<br/>个人中心视图模型]
    end

    subgraph "CommonBusiness业务组件"
        F1[BaseHomeView<br/>基础首页视图]
        F2[BannerCard<br/>横幅卡片]
        F3[TabBarType<br/>标签栏类型]
        F4[BaseVM<br/>基础视图模型]
    end

    subgraph "Common基础组件"
        G1[BaseButton<br/>基础按钮]
        G2[TopNavigationView<br/>顶部导航]
        G3[Logger<br/>日志工具]
        G4[WindowUtil<br/>窗口工具]
        G5[PreferenceManager<br/>偏好设置]
        G6[BreakpointType<br/>断点类型]
    end

    %% Phone模块内部依赖
    A1 --> A2
    A1 --> A3
    A2 --> A4
    A2 --> A5

    %% Phone模块对Feature模块的依赖
    A2 --> B1
    A2 --> C1
    A2 --> D1
    A2 --> E1
    A4 --> F3
    A5 --> F3

    %% Feature模块内部依赖
    B1 --> B2
    B1 --> B3
    C1 --> C2
    C1 --> C3
    D1 --> D2
    D1 --> D3
    E1 --> E2
    E1 --> E3

    %% Feature模块对CommonBusiness的依赖
    B1 --> F1
    B1 --> F2
    B3 --> F4
    C1 --> F1
    C3 --> F4
    D1 --> F1
    D3 --> F4

    %% Feature模块对Common的依赖
    B1 --> G2
    B2 --> G3
    B3 --> G3
    C1 --> G2
    C2 --> G3
    C3 --> G3
    D1 --> G2
    D2 --> G3
    D3 --> G3
    E1 --> G1
    E1 --> G2
    E2 --> G3
    E3 --> G3

    %% CommonBusiness对Common的依赖
    F1 --> G2
    F1 --> G6
    F2 --> G1
    F4 --> G3

    %% Phone模块对Common的直接依赖
    A1 --> G3
    A1 --> G4
    A1 --> G5
    A2 --> G3
    A2 --> G4
    A2 --> G6
    A3 --> G3
    A3 --> G4
    A4 --> G6
    A5 --> G6
```

#### 数据流向图
```mermaid
sequenceDiagram
    participant User as 用户操作
    participant Phone as Phone主模块
    participant Feature as Feature模块
    participant Business as CommonBusiness
    participant Common as Common基础
    participant System as HarmonyOS系统

    User->>Phone: 点击标签/启动应用
    Phone->>Common: 调用Logger记录日志
    Phone->>Common: 调用WindowUtil管理窗口
    Phone->>Feature: 加载对应功能视图
    Feature->>Business: 使用通用业务组件
    Business->>Common: 调用基础组件和工具
    Common->>System: 调用系统API
    System-->>Common: 返回系统响应
    Common-->>Business: 返回处理结果
    Business-->>Feature: 返回业务数据
    Feature-->>Phone: 返回视图内容
    Phone-->>User: 显示界面更新

    Note over Phone,Feature: 状态通过AppStorage共享
    Note over Feature,Common: MVVM架构数据绑定
    Note over Common,System: 系统能力封装
```

#### 详细依赖关系说明

##### 1. Phone主模块依赖关系
**文件位置**：`Multiverse/Phone/oh-package.json5`
```json5
{
  "dependencies": {
    "@ohos/home": "file:../../Features/Home",
    "@ohos/security": "file:../../Features/Security",
    "@ohos/community": "file:../../Features/Community",
    "@ohos/mine": "file:../../Features/Mine",
    "@ohos/commonbusiness": "file:../../Features/CommonBusiness",
    "@ohos/common": "file:../../Common"
  }
}
```

**依赖说明**：
- **直接依赖**：所有Feature模块 + CommonBusiness + Common
- **依赖原因**：作为应用入口，需要集成所有功能模块
- **使用方式**：
  ```typescript
  // 导入各功能模块的主视图
  import { ComponentListView } from '@ohos/home';
  import { PracticesView } from '@ohos/security';
  import { ExplorationView } from '@ohos/community';
  import { MineView } from '@ohos/mine';

  // 导入通用业务组件
  import { TabBarType, TAB_CONTENT_STATUSES } from '@ohos/commonbusiness';

  // 导入基础工具
  import { Logger, WindowUtil, PageContext } from '@ohos/common';
  ```

##### 2. Home模块依赖关系
**文件位置**：`Features/Home/oh-package.json5`
```json5
{
  "dependencies": {
    "@ohos/commonbusiness": "file:../CommonBusiness",
    "@ohos/common": "file:../../Common"
  }
}
```

**依赖说明**：
- **直接依赖**：CommonBusiness + Common
- **依赖原因**：
  - CommonBusiness：使用BaseHomeView、BannerCard等通用业务组件
  - Common：使用基础UI组件、工具类、常量定义
- **使用方式**：
  ```typescript
  // 使用通用业务组件
  import { BaseHomeView, BannerCard } from '@ohos/commonbusiness';

  // 使用基础组件和工具
  import { TopNavigationView, Logger, CommonConstants } from '@ohos/common';
  ```

##### 3. Security模块依赖关系
**文件位置**：`Features/Security/oh-package.json5`
```json5
{
  "dependencies": {
    "@ohos/commonbusiness": "file:../CommonBusiness",
    "@ohos/common": "file:../../Common"
  }
}
```

**依赖说明**：
- **直接依赖**：CommonBusiness + Common
- **依赖原因**：
  - CommonBusiness：使用BaseHomeView、通用数据模型
  - Common：使用基础UI组件、工具类、MVVM基类
- **使用方式**：
  ```typescript
  // 使用通用业务组件
  import { BaseHomeView } from '@ohos/commonbusiness';

  // 使用基础架构
  import { BaseVM, BaseState, Logger } from '@ohos/common';
  ```

##### 4. Community模块依赖关系
**文件位置**：`Features/Community/oh-package.json5`
```json5
{
  "dependencies": {
    "@ohos/commonbusiness": "file:../CommonBusiness",
    "@ohos/common": "file:../../Common"
  }
}
```

**依赖说明**：
- **直接依赖**：CommonBusiness + Common
- **依赖原因**：
  - CommonBusiness：使用BaseHomeView、通用业务逻辑
  - Common：使用基础组件、工具类、状态管理
- **使用方式**：
  ```typescript
  // 使用通用业务组件
  import { BaseHomeView } from '@ohos/commonbusiness';

  // 使用基础功能
  import { Logger, PreferenceManager, CommonConstants } from '@ohos/common';
  ```

##### 5. Mine模块依赖关系
**文件位置**：`Features/Mine/oh-package.json5`
```json5
{
  "dependencies": {
    "@ohos/common": "file:../../Common"
  }
}
```

**依赖说明**：
- **直接依赖**：仅依赖Common
- **依赖原因**：个人中心功能相对独立，只需要基础组件和工具
- **使用方式**：
  ```typescript
  // 使用基础组件
  import { TopNavigationView, BaseButton } from '@ohos/common';

  // 使用工具类
  import { Logger, BundleManagerUtil, ProcessUtil } from '@ohos/common';
  ```

##### 6. CommonBusiness模块依赖关系
**文件位置**：`Features/CommonBusiness/oh-package.json5`
```json5
{
  "dependencies": {
    "@ohos/common": "file:../../Common"
  }
}
```

**依赖说明**：
- **直接依赖**：仅依赖Common
- **依赖原因**：作为业务层，基于Common提供更高级的业务组件
- **使用方式**：
  ```typescript
  // 继承基础组件
  import { BaseVM, BaseState, CommonConstants } from '@ohos/common';

  // 使用基础工具
  import { Logger, BreakpointTypeEnum } from '@ohos/common';
  ```

##### 7. Common模块依赖关系
**文件位置**：`Common/oh-package.json5`
```json5
{
  "dependencies": {
    // 仅依赖HarmonyOS系统API，无其他模块依赖
  }
}
```

**依赖说明**：
- **直接依赖**：无其他业务模块依赖
- **系统依赖**：
  ```typescript
  // HarmonyOS系统API
  import { UIAbility, Want, AbilityConstant } from '@kit.AbilityKit';
  import { window, Configuration } from '@kit.ArkUI';
  import { BusinessError } from '@kit.BasicServicesKit';
  ```

#### 依赖传递关系分析

##### 1. 依赖传递链
```
Phone → Home → CommonBusiness → Common → HarmonyOS SDK
Phone → Security → CommonBusiness → Common → HarmonyOS SDK
Phone → Community → CommonBusiness → Common → HarmonyOS SDK
Phone → Mine → Common → HarmonyOS SDK
Phone → CommonBusiness → Common → HarmonyOS SDK
Phone → Common → HarmonyOS SDK
```

##### 2. 循环依赖检查
**当前状态**：✅ 无循环依赖
- 所有依赖关系都是单向的
- 遵循分层架构原则
- 基础层不依赖上层模块

**潜在风险**：
- ❌ 避免Feature模块之间相互依赖
- ❌ 避免Common模块依赖业务模块
- ❌ 避免CommonBusiness依赖具体Feature模块

##### 3. 依赖注入机制

**全局状态注入**：
```typescript
// 在Phone模块的EntryAbility中初始化全局状态
AppStorage.setOrCreate('GlobalInfoModel', new GlobalInfoModel());
AppStorage.setOrCreate('pageContext', new PageContext());

// 各Feature模块通过@StorageProp获取
@StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel;
```

**服务注入**：
```typescript
// Common模块提供单例服务
export class ServiceManager {
  private static instance: ServiceManager;

  static getInstance(): ServiceManager {
    if (!ServiceManager.instance) {
      ServiceManager.instance = new ServiceManager();
    }
    return ServiceManager.instance;
  }
}

// Feature模块使用服务
const serviceManager = ServiceManager.getInstance();
```

#### 模块接口定义

##### 1. Common模块对外接口
```typescript
// 基础组件接口
export interface BaseComponentProps {
  width?: Length;
  height?: Length;
  backgroundColor?: ResourceColor;
}

// 工具类接口
export interface LoggerInterface {
  info(tag: string, message: string): void;
  error(tag: string, message: string): void;
  debug(tag: string, message: string): void;
}

// 状态管理接口
export interface BaseState {
  loading?: boolean;
  error?: string;
}

export abstract class BaseVM<T extends BaseState> {
  protected state: T;
  abstract handleEvent(eventType: string, data?: any): void;
}
```

##### 2. CommonBusiness模块对外接口
```typescript
// 业务组件接口
export interface BusinessComponentProps extends BaseComponentProps {
  data?: any;
  onAction?: (action: string, data?: any) => void;
}

// 业务数据模型接口
export interface BusinessDataItem {
  id: string;
  title: string;
  description?: string;
  icon?: Resource;
}

// 标签栏类型定义
export enum TabBarType {
  HOME = 0,
  SAMPLE = 1,
  PRACTICE = 2,
  MINE = 3
}
```

##### 3. Feature模块对外接口
```typescript
// Home模块接口
export interface ComponentListViewProps {
  onItemClick?: (item: ComponentItem) => void;
}

// Security模块接口
export interface PracticesViewProps {
  onPracticeSelect?: (practice: PracticeItem) => void;
}

// Community模块接口
export interface ExplorationViewProps {
  onContentSelect?: (content: ContentItem) => void;
}

// Mine模块接口
export interface MineViewProps {
  onSettingChange?: (setting: string, value: any) => void;
}
```

#### 版本兼容性管理

##### 1. 模块版本策略
```json5
// 各模块版本号管理
{
  "@ohos/common": "1.0.0",           // 基础模块，变更影响所有模块
  "@ohos/commonbusiness": "1.0.0",   // 业务模块，变更影响Feature模块
  "@ohos/home": "1.0.0",             // 功能模块，独立版本
  "@ohos/security": "1.0.0",         // 功能模块，独立版本
  "@ohos/community": "1.0.0",        // 功能模块，独立版本
  "@ohos/mine": "1.0.0",             // 功能模块，独立版本
  "@ohos/phone": "1.0.0"             // 主模块，集成所有模块
}
```

##### 2. 兼容性矩阵
| 模块 | Common | CommonBusiness | Home | Security | Community | Mine |
|------|--------|----------------|------|----------|-----------|------|
| Common | - | ✅ | ✅ | ✅ | ✅ | ✅ |
| CommonBusiness | 依赖 | - | ✅ | ✅ | ✅ | ❌ |
| Home | 依赖 | 依赖 | - | ❌ | ❌ | ❌ |
| Security | 依赖 | 依赖 | ❌ | - | ❌ | ❌ |
| Community | 依赖 | 依赖 | ❌ | ❌ | - | ❌ |
| Mine | 依赖 | ❌ | ❌ | ❌ | ❌ | - |

##### 3. 破坏性变更处理
**Common模块变更**：
```typescript
// 向后兼容的方式添加新功能
export interface BaseComponentPropsV2 extends BaseComponentProps {
  // 新增属性使用可选类型
  newProperty?: string;
}

// 废弃旧接口时提供过渡期
/** @deprecated 使用newMethod替代，将在v2.0.0中移除 */
export function oldMethod(): void {
  // 内部调用新方法
  newMethod();
}

export function newMethod(): void {
  // 新实现
}
```

**Feature模块变更**：
```typescript
// 功能模块可以独立升级
// 通过接口版本控制兼容性
export const MODULE_VERSION = '1.1.0';
export const SUPPORTED_COMMON_VERSION = '^1.0.0';
```

#### 依赖优化建议

##### 1. 减少依赖层级
- ✅ 保持依赖链路简短（最多3-4层）
- ✅ 避免不必要的中间层
- ✅ 合理抽象公共功能

##### 2. 按需导入
```typescript
// ❌ 避免全量导入
import * from '@ohos/common';

// ✅ 推荐按需导入
import { Logger, WindowUtil } from '@ohos/common';
```

##### 3. 懒加载策略
```typescript
// 动态导入减少初始加载时间
const loadFeatureModule = async () => {
  const module = await import('@ohos/feature-module');
  return module;
};
```

##### 4. 依赖注入优化
```typescript
// 使用依赖注入容器管理复杂依赖
export class DIContainer {
  private services = new Map<string, any>();

  register<T>(key: string, service: T): void {
    this.services.set(key, service);
  }

  resolve<T>(key: string): T {
    return this.services.get(key);
  }
}
```

## 模块详细说明

### 1. AppScope - 应用级配置
**位置**：`AppScope/`
**用途**：定义应用的全局配置信息
**主要文件**：
- `app.json5` - 应用基本信息配置

**配置内容**：
```json5
{
  "app": {
    "bundleName": "com.sucorde.almighty",    // 应用包名
    "vendor": "example",                      // 开发商
    "versionCode": 1000000,                   // 版本代码
    "versionName": "1.0.0",                   // 版本名称
    "icon": "$media:layered_image",           // 应用图标
    "label": "$string:app_name"               // 应用名称
  }
}
```

**调用方法**：由系统自动读取，无需手动调用
**修改方法**：直接编辑app.json5文件中的配置项

### 2. Common - 通用基础模块
**位置**：`Common/`
**用途**：提供整个应用的基础工具类、通用组件、常量定义等
**模块类型**：HAR (HarmonyOS Ability Resource)

**主要功能**：
- 基础UI组件（按钮、输入框、导航栏等）
- 工具类（日志、网络、存储、窗口管理等）
- 常量定义（尺寸、颜色、字符串等）
- 基础数据模型和状态管理
- 响应式设计支持

**调用方法**：
```typescript
// 导入通用组件
import { BaseButton, TopNavigationView } from '@ohos/common';

// 导入工具类
import { Logger, WindowUtil, PreferenceManager } from '@ohos/common';

// 导入常量
import { CommonConstants, BreakpointTypeEnum } from '@ohos/common';
```

**修改方法**：
- 添加新组件：在`src/main/ets/component/`目录下创建新文件
- 添加新工具：在`src/main/ets/util/`目录下创建新文件
- 修改常量：编辑`src/main/ets/constants/`目录下的文件
- 更新导出：在`Index.ets`中添加新的export语句

**依赖关系**：
- 被所有Feature模块和Phone模块依赖
- 不依赖其他业务模块，只依赖系统API

### 3. Features - 功能特性模块集合

#### 3.1 Home - 首页模块
**位置**：`Features/Home/`
**用途**：应用首页功能，展示组件列表和主要功能入口
**模块类型**：HAR

**主要功能**：
- 组件列表展示
- 功能导航入口
- 搜索和筛选
- 响应式布局适配

**调用方法**：
```typescript
// 导入首页视图
import { ComponentListView } from '@ohos/home';

// 在页面中使用
TabContent() {
  ComponentListView()
}
```

**修改方法**：
- 添加新组件：在组件数据模型中添加新项
- 修改布局：编辑ComponentListView组件
- 更新数据：修改ComponentListModel中的数据源

**依赖关系**：
- 依赖：@ohos/common、@ohos/commonbusiness
- 被依赖：@ohos/phone

#### 3.2 Security - 安全模块
**位置**：`Features/Security/`
**用途**：安全相关功能，包括示例展示和安全实践
**模块类型**：HAR

**主要功能**：
- 安全示例展示
- 最佳实践指南
- 安全工具集成
- 教育内容展示

**调用方法**：
```typescript
// 导入安全视图
import { PracticesView } from '@ohos/security';

// 在页面中使用
TabContent() {
  PracticesView()
}
```

**修改方法**：
- 添加新示例：在示例数据模型中添加新项
- 修改内容：编辑PracticesView组件
- 更新数据：修改SampleModel中的数据源

**依赖关系**：
- 依赖：@ohos/common、@ohos/commonbusiness
- 被依赖：@ohos/phone

#### 3.3 Community - 社区模块
**位置**：`Features/Community/`
**用途**：社区功能，包括内容探索和用户交互
**模块类型**：HAR

**主要功能**：
- 内容探索和发现
- 用户交互功能
- 社区动态展示
- 个性化推荐

**调用方法**：
```typescript
// 导入社区视图
import { ExplorationView } from '@ohos/community';

// 在页面中使用
TabContent() {
  ExplorationView()
}
```

**修改方法**：
- 添加新内容：在发现数据模型中添加新项
- 修改界面：编辑ExplorationView组件
- 更新数据：修改DiscoverModel中的数据源

**依赖关系**：
- 依赖：@ohos/common、@ohos/commonbusiness
- 被依赖：@ohos/phone

#### 3.4 Mine - 个人中心模块
**位置**：`Features/Mine/`
**用途**：用户个人信息管理和应用设置
**模块类型**：HAR

**主要功能**：
- 个人信息展示
- 应用设置管理
- 关于页面
- 版本更新检查

**调用方法**：
```typescript
// 导入个人中心视图
import { MineView } from '@ohos/mine';

// 在页面中使用
TabContent() {
  MineView()
}
```

**修改方法**：
- 添加新功能：在MineView中添加新的CardItem
- 修改设置：编辑相关的状态和视图模型
- 更新关于信息：修改AboutView组件

**依赖关系**：
- 依赖：@ohos/common
- 被依赖：@ohos/phone

#### 3.5 CommonBusiness - 通用业务模块
**位置**：`Features/CommonBusiness/`
**用途**：提供跨模块的通用业务组件和逻辑
**模块类型**：HAR

**主要功能**：
- 通用业务组件（Banner、Card、Navigation等）
- 业务数据模型
- 通用业务逻辑
- MVVM架构支持

**调用方法**：
```typescript
// 导入通用业务组件
import { BannerCard, BaseHomeView } from '@ohos/commonbusiness';

// 导入业务常量
import { TabBarType, TAB_CONTENT_STATUSES } from '@ohos/commonbusiness';
```

**修改方法**：
- 添加新组件：在component目录下创建新组件
- 添加新模型：在model目录下创建新数据模型
- 修改业务逻辑：编辑viewmodel目录下的文件

**依赖关系**：
- 依赖：@ohos/common
- 被依赖：@ohos/home、@ohos/security、@ohos/community、@ohos/phone

### 4. Multiverse - 多设备适配

#### 4.1 Phone - 手机应用主模块
**位置**：`Multiverse/Phone/`
**用途**：手机设备的应用主入口，负责整个应用的生命周期管理
**模块类型**：Entry Module

**主要功能**：
- 应用入口能力（EntryAbility）
- 主页面导航（MainPage）
- 启动页面（SplashPage）
- 标签栏导航（CustomTabBar）
- 侧边栏导航（CustomSideBar）
- 响应式布局管理

**调用方法**：
```typescript
// 作为应用入口，由系统自动启动
// 其他模块通过依赖关系被Phone模块调用

// 页面导航示例
const pageContext: PageContext = AppStorage.get('pageContext') as PageContext;
pageContext.pushPage({
  routerName: 'TargetPage',
  param: { /* 参数 */ }
});
```

**修改方法**：
- 添加新页面：在pages目录下创建新页面文件
- 修改导航：编辑MainPage中的标签配置
- 更新权限：修改module.json5中的权限配置
- 添加新能力：在extensionAbilities中添加新能力

**依赖关系**：
- 依赖：所有Feature模块和Common模块
- 被依赖：无（作为应用入口）

## 模块间通信机制

### 1. 数据流向
```
用户操作 → Phone主模块 → 对应Feature模块 → CommonBusiness → Common
                ↓
            状态更新 ← 数据处理 ← 业务逻辑 ← 基础服务
```

### 2. 状态管理
**全局状态管理**：使用AppStorage进行跨模块状态共享
```typescript
// 设置全局状态
AppStorage.setOrCreate('globalState', value);

// 监听状态变化
@StorageProp('globalState') globalState: Type = AppStorage.get('globalState');
```

**页面上下文管理**：
```typescript
// 页面导航上下文
AppStorage.setOrCreate('pageContext', new PageContext());

// 组件列表页面上下文
AppStorage.setOrCreate('componentListPageContext', new PageContext());

// 示例页面上下文
AppStorage.setOrCreate('samplePageContext', new PageContext());

// 探索页面上下文
AppStorage.setOrCreate('explorationPageContext', new PageContext());
```

### 3. 事件通信
**MVVM事件驱动**：
```typescript
// 发送事件
viewModel.sendEvent(EventType.ACTION_TYPE, data);

// 处理事件
handleEvent(eventType: EventType, data?: any): void {
  switch (eventType) {
    case EventType.ACTION_TYPE:
      // 处理逻辑
      break;
  }
}
```

## 开发指南

### 1. 环境搭建
**必需工具**：
- DevEco Studio 4.0+
- HarmonyOS SDK API 10+
- Node.js 16+

**项目初始化**：
```bash
# 克隆项目
git clone <repository-url>

# 进入项目目录
cd Almighty

# 安装依赖
ohpm install

# 构建项目
hvigor build
```

### 2. 开发流程

#### 添加新功能模块
1. **创建模块目录**：
```bash
mkdir Features/NewModule
cd Features/NewModule
```

2. **初始化模块配置**：
```json5
// oh-package.json5
{
  "name": "@ohos/newmodule",
  "version": "1.0.0",
  "main": "Index.ets",
  "dependencies": {
    "@ohos/common": "file:../../Common",
    "@ohos/commonbusiness": "file:../CommonBusiness"
  }
}
```

3. **创建模块结构**：
```
NewModule/
├── src/main/ets/
│   ├── component/          # 组件
│   ├── model/             # 数据模型
│   ├── viewmodel/         # 视图模型
│   └── view/              # 视图
├── Index.ets              # 导出文件
└── oh-package.json5       # 模块配置
```

4. **在Phone模块中集成**：
```typescript
// 添加依赖到Phone/oh-package.json5
"dependencies": {
  "@ohos/newmodule": "file:../../Features/NewModule"
}

// 在MainPage中添加标签
TabContent() {
  NewModuleView()
}
```

#### 添加新页面
1. **创建页面文件**：
```typescript
// Features/Module/src/main/ets/view/NewPage.ets
@Component
export struct NewPage {
  build() {
    // 页面内容
  }
}
```

2. **注册页面路由**：
```typescript
// 在对应的PageContext中注册
pageContext.registerPage('NewPage', () => import('./view/NewPage'));
```

3. **导航到页面**：
```typescript
pageContext.pushPage({
  routerName: 'NewPage',
  param: { /* 参数 */ }
});
```

#### 添加新组件
1. **创建组件文件**：
```typescript
// Common/src/main/ets/component/NewComponent.ets
@Component
export struct NewComponent {
  @Prop data: DataType;

  build() {
    // 组件内容
  }
}
```

2. **导出组件**：
```typescript
// Common/Index.ets
export { NewComponent } from './src/main/ets/component/NewComponent';
```

3. **使用组件**：
```typescript
import { NewComponent } from '@ohos/common';

NewComponent({ data: this.componentData })
```

### 3. 数据管理

#### 添加新数据模型
1. **创建模型文件**：
```typescript
// Common/src/main/ets/model/NewDataModel.ets
export interface NewDataItem {
  id: string;
  name: string;
  description: string;
}

export class NewDataModel {
  private static instance: NewDataModel;
  private dataList: NewDataItem[] = [];

  static getInstance(): NewDataModel {
    if (!NewDataModel.instance) {
      NewDataModel.instance = new NewDataModel();
    }
    return NewDataModel.instance;
  }

  getData(): NewDataItem[] {
    return this.dataList;
  }

  addData(item: NewDataItem): void {
    this.dataList.push(item);
  }
}
```

2. **在视图模型中使用**：
```typescript
export class NewViewModel extends BaseVM<BaseState> {
  private dataModel: NewDataModel = NewDataModel.getInstance();

  loadData(): void {
    const data = this.dataModel.getData();
    // 处理数据
  }
}
```

### 4. 样式和主题

#### 响应式设计
```typescript
// 使用断点系统
@StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel;

// 根据断点调整样式
.width(new BreakpointType({
  sm: '100%',
  md: '80%',
  lg: '60%',
  xl: '50%'
}).getValue(this.globalInfoModel.currentBreakpoint))
```

#### 主题适配
```typescript
// 监听颜色模式变化
@StorageProp('systemColorMode') @Watch('onColorModeChange')
systemColorMode: ConfigurationConstant.ColorMode;

onColorModeChange(): void {
  // 处理主题变化
}
```

### 5. 性能优化

#### 组件优化
```typescript
// 启用组件冻结
@Component({ freezeWhenInactive: true })
struct OptimizedComponent {
  // 组件实现
}

// 使用LazyForEach优化列表
LazyForEach(this.dataSource, (item: DataItem) => {
  ItemComponent({ data: item })
}, (item: DataItem) => item.id)
```

#### 资源预加载
```typescript
// 在启动页预加载资源
private preloadResources(): void {
  this.componentListModel.preloadComponentData();
  this.sampleModel.preloadSamplePageData();
  this.discoverModel.preloadDiscoveryData();
}
```

## 测试指南

### 1. 单元测试
```typescript
// 测试文件：src/test/ets/TestSpec.ets
import { describe, beforeEach, it, expect } from '@ohos/hypium';

describe('ComponentTest', () => {
  beforeEach(() => {
    // 测试前准备
  });

  it('should render correctly', () => {
    // 测试逻辑
    expect(result).assertEqual(expected);
  });
});
```

### 2. 集成测试
```bash
# 运行测试
hvigor test

# 运行特定测试
hvigor test --tests TestSpec
```

### 3. 性能测试
```typescript
// 性能监控
import { hiTraceMeter } from '@kit.PerformanceAnalysisKit';

hiTraceMeter.startTrace('operation_name', 1001);
// 执行操作
hiTraceMeter.finishTrace('operation_name', 1001);
```

## 构建和部署

### 1. 构建配置
```json5
// build-profile.json5
{
  "apiType": "stageMode",
  "buildOption": {
    "strictMode": {
      "caseSensitiveCheck": true,
      "useNormalizedOHMUrl": true
    }
  },
  "targets": [
    {
      "name": "default",
      "runtimeOS": "HarmonyOS"
    }
  ]
}
```

### 2. 构建命令
```bash
# 清理构建
hvigor clean

# 构建项目
hvigor build

# 构建发布版本
hvigor build --mode release

# 打包HAP
hvigor assembleHap
```

### 3. 部署流程
```bash
# 安装到设备
hdc install entry-default-signed.hap

# 启动应用
hdc shell aa start -a EntryAbility -b com.sucorde.almighty

# 查看日志
hdc hilog
```

## 版本管理

### 1. 版本号规则
- **主版本号**：重大架构变更
- **次版本号**：新功能添加
- **修订版本号**：Bug修复和小改进

### 2. 发布流程
1. 更新版本号（app.json5和各模块的oh-package.json5）
2. 更新CHANGELOG.md
3. 运行完整测试套件
4. 构建发布版本
5. 创建Git标签
6. 发布到应用市场

## 常见问题

### 1. 模块依赖问题
**问题**：模块导入失败
**解决**：检查oh-package.json5中的依赖路径是否正确

### 2. 构建失败
**问题**：构建过程中出现错误
**解决**：
- 检查API版本兼容性
- 清理构建缓存：`hvigor clean`
- 重新安装依赖：`ohpm install`

### 3. 性能问题
**问题**：应用启动慢或运行卡顿
**解决**：
- 启用组件冻结功能
- 优化资源加载策略
- 使用LazyForEach优化列表渲染

### 4. 适配问题
**问题**：在不同设备上显示异常
**解决**：
- 检查断点监听是否正确设置
- 验证响应式布局配置
- 测试多设备兼容性

## 贡献指南

### 1. 代码规范
- 使用TypeScript严格模式
- 遵循ArkTS编码规范
- 保持代码注释完整
- 使用有意义的变量和函数名

### 2. 提交规范
```
feat: 添加新功能
fix: 修复Bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建配置更新
```

### 3. 开发流程
1. Fork项目
2. 创建功能分支
3. 开发和测试
4. 提交Pull Request
5. 代码审查
6. 合并到主分支

---

**注意**：本项目持续更新中，请关注最新的文档和版本变更。如有问题或建议，请通过Issue系统反馈。
