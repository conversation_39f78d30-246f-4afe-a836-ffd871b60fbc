# Common通用工具模块说明文档

## 概述
Common模块是HarmonyOS应用的通用工具库，提供了应用开发中常用的工具类、组件、模型和服务。该模块作为基础设施层，为其他功能模块提供统一的工具支持，包括窗口管理、日志记录、断点系统、数据存储、网络请求、UI组件等核心功能。

## 目录结构

### 根目录文件

#### 配置文件
- **BuildProfile.ets** - 构建配置文件
  - 用途：定义构建时的版本信息和调试模式常量
  - 调用方法：`import BuildProfile from './BuildProfile'`
  - 数据更改：修改HAR_VERSION、BUILD_MODE_NAME、DEBUG等常量

- **Index.ets** - 模块入口文件
  - 用途：导出模块的所有公共API和组件
  - 调用方法：`import { WindowUtil, Logger, GlobalInfoModel } from '@ohos/common'`
  - 数据更改：添加新的导出项时在此文件中添加export语句

- **build-profile.json5** - 构建配置
  - 用途：定义模块的构建选项、混淆规则、目标平台等
  - 调用方法：由构建系统自动读取
  - 数据更改：修改apiType、buildOption、targets等配置项

- **hvigorfile.ts** - Hvigor构建脚本
  - 用途：定义构建任务和插件配置
  - 调用方法：由Hvigor构建系统自动执行
  - 数据更改：在plugins数组中添加自定义插件

- **oh-package.json5** - 包配置文件
  - 用途：定义模块名称、版本、依赖关系
  - 调用方法：由包管理器自动读取
  - 数据更改：使用包管理器命令添加/删除依赖

#### 规则文件
- **consumer-rules.txt** - 消费者混淆规则
  - 用途：定义哪些文件在混淆时需要保持原样
  - 数据更改：添加需要保持的文件路径

- **obfuscation-rules.txt** - 混淆规则配置
  - 用途：定义代码混淆的具体规则和选项
  - 数据更改：修改混淆选项

### src/main目录结构

#### module.json5
- 用途：定义模块的基本信息和配置
- 调用方法：由系统自动读取
- 数据更改：修改模块名称、类型、支持的设备类型

#### ets目录 - 主要源码目录

##### util目录 - 工具类
- **WindowUtil.ets** - 窗口工具类
  - 用途：提供窗口管理相关功能，包括状态栏设置、全屏模式、窗口方向等
  - 调用方法：`WindowUtil.updateStatusBarColor(context, isDark)`
  - 主要功能：
    - 状态栏颜色设置
    - 全屏模式切换
    - 窗口方向控制
    - 沉浸式状态栏
    - 设备信息获取

- **Logger.ets** - 日志工具类
  - 用途：封装HarmonyOS的hilog功能，提供统一的日志输出接口
  - 调用方法：
    ```typescript
    import Logger from '@ohos/common';
    const logger = new Logger('[MyTag]');
    logger.info('信息日志');
    logger.error('错误日志');
    ```
  - 主要功能：
    - debug、info、warn、error四种日志级别
    - 统一的日志格式
    - 日志域和标签管理

- **BreakpointSystem.ets** - 断点系统
  - 用途：管理响应式布局的断点系统，支持不同屏幕尺寸的适配
  - 调用方法：
    ```typescript
    import { BreakpointType, BreakpointSystem } from '@ohos/common';
    const padding = new BreakpointType({
      sm: 16, md: 24, lg: 32
    }).getValue(currentBreakpoint);
    ```
  - 主要功能：
    - 断点类型定义和管理
    - 响应式值计算
    - 屏幕尺寸监听
    - 设备类型判断

- **BundleManagerUtil.ets** - 包管理工具
  - 用途：提供应用包信息获取和管理功能
  - 调用方法：`BundleManagerUtil.getBundleInfo()`
  - 主要功能：包信息查询、版本管理、权限检查

- **ColorUtil.ets** - 颜色工具类
  - 用途：提供颜色处理和转换功能
  - 调用方法：`ColorUtil.hexToRgb('#FF0000')`
  - 主要功能：颜色格式转换、颜色计算、主题色管理

- **ImageUtil.ets** - 图片工具类
  - 用途：提供图片处理和操作功能
  - 调用方法：`ImageUtil.compressImage(imageData)`
  - 主要功能：图片压缩、格式转换、尺寸调整

- **NetworkUtil.ets** - 网络工具类
  - 用途：提供网络状态检测和管理功能
  - 调用方法：`NetworkUtil.isNetworkAvailable()`
  - 主要功能：网络状态监听、连接类型判断、网络质量检测

- **WebUtil.ets** - Web工具类
  - 用途：提供Web组件相关的工具功能
  - 调用方法：`WebUtil.createWebController()`
  - 主要功能：Web控制器管理、JavaScript交互、Web内容处理

- **GlobalContext.ets** - 全局上下文
  - 用途：提供全局上下文管理功能
  - 调用方法：`GlobalContext.getContext()`
  - 主要功能：上下文存储、全局状态管理

- **ProcessUtil.ets** - 进程工具类
  - 用途：提供进程相关的工具功能
  - 调用方法：`ProcessUtil.getCurrentProcessInfo()`
  - 主要功能：进程信息获取、进程管理

- **VibratorUtils.ets** - 振动工具类
  - 用途：提供设备振动功能
  - 调用方法：`VibratorUtils.vibrate(duration)`
  - 主要功能：振动控制、振动模式设置

- **ResourceUtil.ets** - 资源工具类
  - 用途：提供资源文件访问和管理功能
  - 调用方法：`ResourceUtil.getString(resId)`
  - 主要功能：资源文件读取、多语言支持、配置管理

- **DynamicInstallManager.ets** - 动态安装管理器
  - 用途：提供动态功能模块的安装和管理
  - 调用方法：`DynamicInstallManager.installModule(moduleName)`
  - 主要功能：模块动态加载、安装状态管理

- **ColorPickerUtil.ets** - 颜色选择器工具
  - 用途：提供颜色选择和处理功能
  - 调用方法：`ColorPickerUtil.pickColor()`
  - 主要功能：颜色选择、颜色分析、调色板管理

- **ObservedArray.ets** - 可观察数组
  - 用途：提供可观察的数组实现，支持数据绑定
  - 调用方法：`new ObservedArray<T>()`
  - 主要功能：数组变化监听、数据绑定、状态同步

##### model目录 - 数据模型
- **GlobalInfoModel.ets** - 全局信息模型
  - 用途：存储和管理应用的全局状态信息
  - 调用方法：`AppStorage.get('GlobalInfoModel')`
  - 数据结构：
    ```typescript
    @Observed
    class GlobalInfoModel {
      foldExpanded: boolean;              // 折叠屏展开状态
      currentBreakpoint: BreakpointTypeEnum; // 当前断点类型
      naviIndicatorHeight: number;        // 导航指示器高度
      statusBarHeight: number;            // 状态栏高度
      decorHeight: number;                // 装饰区域高度
      deviceHeight: number;               // 设备屏幕高度
      deviceWidth: number;                // 设备屏幕宽度
    }
    ```

- **LoadingModel.ets** - 加载状态模型
  - 用途：管理页面和组件的加载状态
  - 调用方法：`new LoadingModel()`
  - 数据结构：包含加载状态、错误信息、是否有更多数据等属性

- **ResponseData.ets** - 响应数据模型
  - 用途：定义网络请求响应的数据结构
  - 调用方法：`ResponseData<T>`
  - 数据结构：包含状态码、消息、数据等属性

- **BundleInfoData.ets** - 包信息数据模型
  - 用途：定义应用包信息的数据结构
  - 调用方法：`new BundleInfoData()`
  - 数据结构：包含包名、版本、权限等信息

##### component目录 - UI组件
- **TopNavigationView.ets** - 顶部导航视图
  - 用途：提供标准的顶部导航栏组件
  - 调用方法：
    ```typescript
    TopNavigationView({
      topNavigationData: {
        title: '页面标题',
        showBackButton: true
      },
      menuView: () => {
        // 自定义菜单视图
      }
    })
    ```
  - 主要功能：
    - 标题显示和自适应
    - 返回按钮
    - 自定义菜单区域
    - 响应式布局
    - 模糊背景效果

- **LoadingMore.ets** - 加载更多组件
  - 用途：提供列表底部的加载更多指示器
  - 调用方法：`LoadingMore({ isLoading: this.isLoadingMore })`
  - 主要功能：加载动画、状态提示

- **NoMore.ets** - 没有更多组件
  - 用途：提供列表底部的没有更多数据提示
  - 调用方法：`NoMore()`
  - 主要功能：结束提示、样式统一

- **WebSheet.ets** - Web弹窗组件
  - 用途：提供Web内容的弹窗展示功能
  - 调用方法：`WebSheetBuilder.show(url, type)`
  - 主要功能：Web内容展示、弹窗管理、URL处理

- **VideoComponent.ets** - 视频组件
  - 用途：提供视频播放功能
  - 调用方法：`VideoComponent({ src: videoUrl })`
  - 主要功能：视频播放、控制器、全屏模式

##### view目录 - 视图组件
- **LoadingView.ets** - 加载视图
  - 用途：提供统一的加载状态页面
  - 调用方法：`LoadingView()`
  - 主要功能：加载动画、状态提示

- **LoadingFailedView.ets** - 加载失败视图
  - 用途：提供统一的加载失败页面
  - 调用方法：`LoadingFailedView({ onRetry: this.retry })`
  - 主要功能：错误提示、重试按钮

- **EmptyContentView.ets** - 空内容视图
  - 用途：提供统一的空数据页面
  - 调用方法：`EmptyContentView({ message: '暂无数据' })`
  - 主要功能：空状态提示、自定义消息

- **NoNetworkView.ets** - 无网络视图
  - 用途：提供统一的无网络连接页面
  - 调用方法：`NoNetworkView({ onRefresh: this.refresh })`
  - 主要功能：网络状态提示、刷新按钮

##### viewmodel目录 - 视图模型
- **BaseVM.ets** - 基础视图模型
  - 用途：提供MVVM架构的基础视图模型抽象类
  - 调用方法：`class MyViewModel extends BaseVM<MyState>`
  - 主要功能：
    - 状态管理
    - 事件处理
    - 生命周期管理
    - 数据绑定

- **BaseState.ets** - 基础状态类
  - 用途：定义视图模型状态的基础结构
  - 调用方法：`class MyState extends BaseState`
  - 主要功能：状态属性定义、状态变化通知

- **BaseVMEvent.ets** - 基础视图模型事件
  - 用途：定义视图模型事件的基础结构
  - 调用方法：`class MyEvent extends BaseVMEvent`
  - 主要功能：事件类型定义、事件数据传递

##### storagemanager目录 - 存储管理
- **MockRequest.ets** - 模拟请求类
  - 用途：提供模拟数据请求功能，用于开发和测试
  - 调用方法：`MockRequest.call<T>(trigger)`
  - 主要功能：
    - 本地JSON数据读取
    - 异步数据模拟
    - 错误处理
    - 数据格式化

- **PreferenceManager.ets** - 偏好设置管理器
  - 用途：提供应用偏好设置的存储和管理
  - 调用方法：`PreferenceManager.set(key, value)`
  - 主要功能：
    - 键值对存储
    - 数据持久化
    - 类型安全
    - 异步操作

##### constant目录 - 常量定义
- **CommonConstants.ets** - 通用常量
  - 用途：定义应用中使用的各种常量值
  - 调用方法：`CommonConstants.SPACE_16`
  - 常量类型：
    - 尺寸常量（间距、高度、宽度）
    - 时间常量（动画时长、延迟时间）
    - 比例常量（宽高比、缩放比例）
    - 百分比常量

- **CommonEnums.ets** - 通用枚举
  - 用途：定义应用中使用的枚举类型
  - 调用方法：`LoadingStatus.LOADING`
  - 枚举类型：
    - 加载状态枚举
    - 模块名称枚举
    - 滚动方向枚举
    - 产品系列枚举
    - 列数枚举

- **ErrorCodeConstants.ets** - 错误码常量
  - 用途：定义请求错误码常量
  - 调用方法：`RequestErrorCode.NETWORK_ERROR`
  - 错误类型：网络错误、服务器错误、客户端错误

##### routermanager目录 - 路由管理
- **PageContext.ets** - 页面上下文
  - 用途：提供页面间的上下文传递和管理
  - 调用方法：`PageContext.setContext(data)`
  - 主要功能：
    - 页面参数传递
    - 上下文状态管理
    - 页面生命周期
    - 导航控制

##### updateservice目录 - 更新服务
- **UpdateService.ets** - 更新服务
  - 用途：提供应用更新检测和管理功能
  - 调用方法：`UpdateService.checkUpdate()`
  - 主要功能：
    - 版本检测
    - 更新下载
    - 安装管理
    - 进度监控

## 使用方法

### 1. 导入模块
```typescript
import { 
  WindowUtil, 
  Logger, 
  GlobalInfoModel, 
  BreakpointType,
  TopNavigationView,
  LoadingView 
} from '@ohos/common';
```

### 2. 使用日志工具
```typescript
const logger = new Logger('[MyModule]');
logger.info('应用启动');
logger.error('发生错误', error);
```

### 3. 使用断点系统
```typescript
const padding = new BreakpointType({
  sm: 16,
  md: 24, 
  lg: 32
}).getValue(globalInfoModel.currentBreakpoint);
```

### 4. 使用顶部导航
```typescript
TopNavigationView({
  topNavigationData: {
    title: '我的页面',
    showBackButton: true,
    onBackClick: () => router.back()
  }
})
```

## 依赖关系

该模块为基础模块，无外部依赖，仅依赖HarmonyOS系统API：
- @kit.ArkUI - 方舟UI工具包
- @kit.AbilityKit - 系统能力工具包
- @kit.BasicServicesKit - 基础服务工具包
- @kit.PerformanceAnalysisKit - 性能分析工具包
- @kit.ArkTS - ArkTS工具包

## 详细技术说明

### 核心工具类详解

#### WindowUtil - 窗口工具类
**文件位置**：`src/main/ets/util/WindowUtil.ets`

**核心功能实现**：
```typescript
export class WindowUtil {
  // 更新状态栏颜色
  public static updateStatusBarColor(context: common.BaseContext, isDark: boolean): void {
    window.getLastWindow(context).then((windowClass: window.Window) => {
      windowClass.setWindowSystemBarProperties({
        statusBarContentColor: isDark ? StatusBarColorType.WHITE : StatusBarColorType.BLACK
      });
    });
  }

  // 设置全屏模式
  public static setFullScreen(context: common.BaseContext, isFullScreen: boolean): void {
    window.getLastWindow(context).then((windowClass: window.Window) => {
      if (isFullScreen) {
        windowClass.setWindowLayoutFullScreen(true);
      } else {
        windowClass.setWindowLayoutFullScreen(false);
      }
    });
  }

  // 设置屏幕方向
  public static setOrientation(context: common.BaseContext, orientation: ScreenOrientation): void {
    window.getLastWindow(context).then((windowClass: window.Window) => {
      windowClass.setPreferredOrientation(orientation);
    });
  }

  // 获取设备信息
  public static getDeviceInfo(): Promise<DeviceInfo> {
    return new Promise((resolve, reject) => {
      try {
        const info = {
          deviceType: deviceInfo.deviceType,
          productSeries: this.getProductSeries(),
          screenDensity: display.getDefaultDisplaySync().densityDPI,
          screenWidth: display.getDefaultDisplaySync().width,
          screenHeight: display.getDefaultDisplaySync().height
        };
        resolve(info);
      } catch (error) {
        reject(error);
      }
    });
  }

  // 初始化全局信息模型
  public static initGlobalInfoModel(context: common.BaseContext): void {
    const globalInfoModel = new GlobalInfoModel();

    // 获取状态栏高度
    window.getLastWindow(context).then((windowClass: window.Window) => {
      const avoidArea = windowClass.getWindowAvoidArea(window.AvoidAreaType.TYPE_SYSTEM);
      globalInfoModel.statusBarHeight = avoidArea.topRect.height;

      // 获取设备尺寸
      const display = windowClass.getWindowProperties().windowRect;
      globalInfoModel.deviceWidth = display.width;
      globalInfoModel.deviceHeight = display.height;

      // 存储到全局存储
      AppStorage.setOrCreate('GlobalInfoModel', globalInfoModel);
    });
  }
}

// 状态栏颜色类型枚举
export enum StatusBarColorType {
  BLACK = '#FF000000',
  WHITE = '#FFFFFFFF'
}

// 屏幕方向枚举
export enum ScreenOrientation {
  PORTRAIT = window.Orientation.PORTRAIT,
  LANDSCAPE = window.Orientation.LANDSCAPE,
  PORTRAIT_INVERTED = window.Orientation.PORTRAIT_INVERTED,
  LANDSCAPE_INVERTED = window.Orientation.LANDSCAPE_INVERTED
}
```

**使用示例**：
```typescript
// 设置状态栏颜色
WindowUtil.updateStatusBarColor(getContext(), true);

// 设置全屏模式
WindowUtil.setFullScreen(getContext(), true);

// 设置屏幕方向
WindowUtil.setOrientation(getContext(), ScreenOrientation.LANDSCAPE);

// 初始化全局信息模型
WindowUtil.initGlobalInfoModel(getContext());
```

#### Logger - 日志工具类
**文件位置**：`src/main/ets/util/Logger.ets`

**完整实现**：
```typescript
import { hilog } from '@kit.PerformanceAnalysisKit';

class Logger {
  private domain: number;
  private prefix: string;
  private format: string = '%{public}s, %{public}s';

  public constructor(prefix: string) {
    this.prefix = prefix;
    this.domain = 0xFF00;
  }

  // 调试级别日志
  public debug(...args: Object[]): void {
    hilog.debug(this.domain, this.prefix, this.format, args);
  }

  // 信息级别日志
  public info(...args: Object[]): void {
    hilog.info(this.domain, this.prefix, this.format, args);
  }

  // 警告级别日志
  public warn(...args: Object[]): void {
    hilog.warn(this.domain, this.prefix, this.format, args);
  }

  // 错误级别日志
  public error(...args: Object[]): void {
    hilog.error(this.domain, this.prefix, this.format, args);
  }

  // 致命错误级别日志
  public fatal(...args: Object[]): void {
    hilog.fatal(this.domain, this.prefix, this.format, args);
  }

  // 检查是否启用调试日志
  public isLoggable(level: hilog.LogLevel): boolean {
    return hilog.isLoggable(this.domain, this.prefix, level);
  }
}

// 创建默认日志实例
const logger = new Logger('[Common]');
export default logger;
```

**使用示例**：
```typescript
// 创建模块专用日志器
const moduleLogger = new Logger('[MyModule]');

// 记录不同级别的日志
moduleLogger.debug('调试信息', { data: 'debug data' });
moduleLogger.info('应用启动', { version: '1.0.0' });
moduleLogger.warn('警告信息', { warning: 'potential issue' });
moduleLogger.error('错误信息', { error: 'error details' });

// 条件日志记录
if (moduleLogger.isLoggable(hilog.LogLevel.DEBUG)) {
  moduleLogger.debug('仅在调试模式下记录');
}
```

#### BreakpointSystem - 断点系统
**文件位置**：`src/main/ets/util/BreakpointSystem.ets`

**核心实现**：
```typescript
// 断点类型接口
export interface BreakpointTypes<T> {
  xs?: T;    // 超小屏幕断点值，可选
  sm: T;     // 小屏幕断点值，必需
  md: T;     // 中等屏幕断点值，必需
  lg: T;     // 大屏幕断点值，必需
  xl?: T;    // 超大屏幕断点值，可选
}

// 断点类型类
export class BreakpointType<T> {
  private xs: T;
  private sm: T;
  private md: T;
  private lg: T;
  private xl: T;

  constructor(breakpoints: BreakpointTypes<T>) {
    this.xs = breakpoints.xs ?? breakpoints.sm;
    this.sm = breakpoints.sm;
    this.md = breakpoints.md;
    this.lg = breakpoints.lg;
    this.xl = breakpoints.xl ?? breakpoints.lg;
  }

  // 根据当前断点获取对应值
  public getValue(currentBreakpoint: BreakpointTypeEnum): T {
    switch (currentBreakpoint) {
      case BreakpointTypeEnum.XS:
        return this.xs;
      case BreakpointTypeEnum.SM:
        return this.sm;
      case BreakpointTypeEnum.MD:
        return this.md;
      case BreakpointTypeEnum.LG:
        return this.lg;
      case BreakpointTypeEnum.XL:
        return this.xl;
      default:
        return this.md;
    }
  }
}

// 断点系统类
export class BreakpointSystem {
  private static instance: BreakpointSystem;
  private listeners: Array<(breakpoint: BreakpointTypeEnum) => void> = [];
  private currentBreakpoint: BreakpointTypeEnum = BreakpointTypeEnum.MD;

  // 断点阈值定义
  private static readonly BREAKPOINTS = {
    xs: 0,      // 0px 及以上
    sm: 320,    // 320px 及以上
    md: 600,    // 600px 及以上
    lg: 840,    // 840px 及以上
    xl: 1440    // 1440px 及以上
  };

  public static getInstance(): BreakpointSystem {
    if (!BreakpointSystem.instance) {
      BreakpointSystem.instance = new BreakpointSystem();
    }
    return BreakpointSystem.instance;
  }

  // 注册断点变化监听器
  public register(callback: (breakpoint: BreakpointTypeEnum) => void): void {
    this.listeners.push(callback);
  }

  // 注销断点变化监听器
  public unregister(callback: (breakpoint: BreakpointTypeEnum) => void): void {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  // 更新断点
  public updateBreakpoint(width: number): void {
    const newBreakpoint = this.calculateBreakpoint(width);
    if (newBreakpoint !== this.currentBreakpoint) {
      this.currentBreakpoint = newBreakpoint;
      this.notifyListeners();
    }
  }

  // 计算当前断点
  private calculateBreakpoint(width: number): BreakpointTypeEnum {
    if (width >= BreakpointSystem.BREAKPOINTS.xl) {
      return BreakpointTypeEnum.XL;
    } else if (width >= BreakpointSystem.BREAKPOINTS.lg) {
      return BreakpointTypeEnum.LG;
    } else if (width >= BreakpointSystem.BREAKPOINTS.md) {
      return BreakpointTypeEnum.MD;
    } else if (width >= BreakpointSystem.BREAKPOINTS.sm) {
      return BreakpointTypeEnum.SM;
    } else {
      return BreakpointTypeEnum.XS;
    }
  }

  // 通知所有监听器
  private notifyListeners(): void {
    this.listeners.forEach(callback => {
      callback(this.currentBreakpoint);
    });
  }

  // 获取当前断点
  public getCurrentBreakpoint(): BreakpointTypeEnum {
    return this.currentBreakpoint;
  }
}
```

**使用示例**：
```typescript
// 创建响应式值
const padding = new BreakpointType({
  sm: 16,
  md: 24,
  lg: 32
});

// 获取当前断点对应的值
const currentPadding = padding.getValue(globalInfoModel.currentBreakpoint);

// 注册断点变化监听
const breakpointSystem = BreakpointSystem.getInstance();
breakpointSystem.register((breakpoint: BreakpointTypeEnum) => {
  console.log('断点变化:', breakpoint);
  // 更新UI布局
  this.updateLayout(breakpoint);
});

// 在窗口尺寸变化时更新断点
window.on('windowSizeChange', (data) => {
  breakpointSystem.updateBreakpoint(data.width);
});
```

### 核心组件详解

#### TopNavigationView - 顶部导航视图
**文件位置**：`src/main/ets/component/TopNavigationView.ets`

**组件实现**：
```typescript
// 顶部导航数据类
export class TopNavigationData {
  title: string = '';                    // 导航标题
  showBackButton: boolean = true;        // 是否显示返回按钮
  backButtonIcon: ResourceStr = $r('sys.media.ohos_ic_back'); // 返回按钮图标
  fontSize?: number;                     // 字体大小
  fontColor: ResourceColor = $r('sys.color.font_primary'); // 字体颜色
  backgroundColor: ResourceColor = Color.Transparent; // 背景颜色
  onBackClick?: () => void;              // 返回按钮点击回调
}

@Component
export struct TopNavigationView {
  @StorageProp('GlobalInfoModel') @Watch('calculateTitleSize') globalInfoModel: GlobalInfoModel;
  @StorageProp('BlurRenderGroup') blurRenderGroup: boolean = false;
  @Prop topNavigationData: TopNavigationData = new TopNavigationData();
  @BuilderParam menuView?: () => void;
  @State fontSize: number = 20;
  @State backIconBgColor: ResourceColor = Color.Transparent;

  aboutToAppear(): void {
    if (this.topNavigationData.fontSize === undefined) {
      this.calculateTitleSize();
    }
    this.updateBackIconBgColor();
  }

  // 计算标题尺寸
  calculateTitleSize(): void {
    const maxWidth = this.globalInfoModel.deviceWidth - BACK_ICON_WIDTH - TOTAL_PADDING;

    // 使用文本测量API计算合适的字体大小
    let fontSize = 20;
    const measureOption: MeasureOptions = {
      textContent: this.topNavigationData.title,
      fontSize: fontSize
    };

    while (fontSize > 12) {
      measureOption.fontSize = fontSize;
      const textSize = MeasureText.measureText(measureOption);

      if (textSize.width <= maxWidth) {
        break;
      }
      fontSize -= 1;
    }

    this.fontSize = fontSize;
  }

  // 更新返回图标背景色
  updateBackIconBgColor(): void {
    this.backIconBgColor = this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
      Color.Transparent : $r('sys.color.comp_background_tertiary');
  }

  build() {
    Row() {
      // 返回按钮
      if (this.topNavigationData.showBackButton) {
        Button({ type: ButtonType.Circle }) {
          Image(this.topNavigationData.backButtonIcon)
            .width(24)
            .height(24)
            .fillColor($r('sys.color.icon_primary'))
        }
        .width(40)
        .height(40)
        .backgroundColor(this.backIconBgColor)
        .onClick(() => {
          if (this.topNavigationData.onBackClick) {
            this.topNavigationData.onBackClick();
          }
        })
      }

      // 标题
      Text(this.topNavigationData.title)
        .fontSize(this.fontSize)
        .fontColor(this.topNavigationData.fontColor)
        .fontWeight(FontWeight.Medium)
        .maxLines(1)
        .textOverflow({ overflow: TextOverflow.Ellipsis })
        .layoutWeight(1)
        .textAlign(TextAlign.Center)

      // 菜单区域
      if (this.menuView) {
        this.menuView()
      } else {
        // 占位空间，保持标题居中
        Blank()
          .width(this.topNavigationData.showBackButton ? 40 : 0)
      }
    }
    .width('100%')
    .height(CommonConstants.NAVIGATION_HEIGHT)
    .padding({
      left: CommonConstants.SPACE_16,
      right: CommonConstants.SPACE_16
    })
    .backgroundColor(this.topNavigationData.backgroundColor)
    .justifyContent(FlexAlign.SpaceBetween)
    .alignItems(VerticalAlign.Center)
  }
}
```

**使用示例**：
```typescript
// 基础用法
TopNavigationView({
  topNavigationData: {
    title: '我的页面',
    showBackButton: true,
    onBackClick: () => {
      router.back();
    }
  }
})

// 带自定义菜单的用法
TopNavigationView({
  topNavigationData: {
    title: '设置页面',
    showBackButton: true,
    fontColor: Color.White,
    backgroundColor: '#FF007DFF'
  },
  menuView: () => {
    Row() {
      Button('保存')
        .type(ButtonType.Normal)
        .fontSize(14)
        .onClick(() => {
          this.saveSettings();
        })

      Button({ type: ButtonType.Circle }) {
        Image($r('app.media.ic_more'))
          .width(24)
          .height(24)
      }
      .width(40)
      .height(40)
      .margin({ left: 8 })
      .onClick(() => {
        this.showMoreMenu();
      })
    }
  }
})

// 响应式标题大小
TopNavigationView({
  topNavigationData: {
    title: '这是一个很长的页面标题，需要自适应显示',
    showBackButton: true,
    // fontSize 不设置，让组件自动计算
  }
})
```

### 数据模型详解

#### GlobalInfoModel - 全局信息模型
**文件位置**：`src/main/ets/model/GlobalInfoModel.ets`

**完整实现**：
```typescript
@Observed
export class GlobalInfoModel {
  // 折叠屏展开状态，true为展开，false为折叠
  public foldExpanded: boolean = false;
  // 当前断点类型，默认为中等屏幕断点
  public currentBreakpoint: BreakpointTypeEnum = BreakpointTypeEnum.MD;
  // 导航指示器高度
  public naviIndicatorHeight: number = 0;
  // 状态栏高度
  public statusBarHeight: number = 0;
  // 装饰区域高度
  public decorHeight: number = 0;
  // 设备屏幕高度
  public deviceHeight: number = 0;
  // 设备屏幕宽度
  public deviceWidth: number = 0;

  // 更新断点信息
  public updateBreakpoint(breakpoint: BreakpointTypeEnum): void {
    this.currentBreakpoint = breakpoint;
  }

  // 更新设备尺寸
  public updateDeviceSize(width: number, height: number): void {
    this.deviceWidth = width;
    this.deviceHeight = height;
  }

  // 更新状态栏高度
  public updateStatusBarHeight(height: number): void {
    this.statusBarHeight = height;
  }

  // 检查是否为大屏设备
  public isLargeScreen(): boolean {
    return this.currentBreakpoint === BreakpointTypeEnum.LG ||
           this.currentBreakpoint === BreakpointTypeEnum.XL;
  }

  // 检查是否为平板设备
  public isTablet(): boolean {
    return this.currentBreakpoint === BreakpointTypeEnum.MD ||
           this.currentBreakpoint === BreakpointTypeEnum.LG;
  }

  // 获取安全区域高度
  public getSafeAreaHeight(): number {
    return this.deviceHeight - this.statusBarHeight - this.naviIndicatorHeight;
  }
}

// 断点类型枚举
export enum BreakpointTypeEnum {
  XS = 'xs',    // 超小屏幕断点 (0-319px)
  SM = 'sm',    // 小屏幕断点 (320-599px)
  MD = 'md',    // 中等屏幕断点 (600-839px)
  LG = 'lg',    // 大屏幕断点 (840-1439px)
  XL = 'xl',    // 超大屏幕断点 (1440px+)
}
```

**使用示例**：
```typescript
// 获取全局信息模型
const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;

// 检查设备类型
if (globalInfoModel.isLargeScreen()) {
  // 大屏设备特殊处理
  this.showSidePanel = true;
}

// 根据断点调整布局
const columns = globalInfoModel.isTablet() ? 2 : 1;

// 计算内容区域高度
const contentHeight = globalInfoModel.getSafeAreaHeight() - this.headerHeight;
```

#### LoadingModel - 加载状态模型
**文件位置**：`src/main/ets/model/LoadingModel.ets`

**完整实现**：
```typescript
@Observed
export class LoadingModel {
  // 加载状态
  public loadingStatus: LoadingStatus = LoadingStatus.LOADING;
  // 错误信息
  public errorMessage: string = '';
  // 是否有更多数据
  public hasMore: boolean = true;
  // 是否正在加载更多
  public isLoadingMore: boolean = false;
  // 总数据量
  public totalCount: number = 0;
  // 当前页码
  public currentPage: number = 1;
  // 每页数据量
  public pageSize: number = 20;

  // 设置加载中状态
  public setLoading(): void {
    this.loadingStatus = LoadingStatus.LOADING;
    this.errorMessage = '';
  }

  // 设置加载成功状态
  public setSuccess(): void {
    this.loadingStatus = LoadingStatus.SUCCESS;
    this.errorMessage = '';
  }

  // 设置加载失败状态
  public setError(message: string): void {
    this.loadingStatus = LoadingStatus.FAILED;
    this.errorMessage = message;
  }

  // 设置空数据状态
  public setEmpty(): void {
    this.loadingStatus = LoadingStatus.EMPTY;
    this.errorMessage = '';
  }

  // 开始加载更多
  public startLoadingMore(): void {
    this.isLoadingMore = true;
  }

  // 结束加载更多
  public stopLoadingMore(hasMore: boolean = true): void {
    this.isLoadingMore = false;
    this.hasMore = hasMore;
  }

  // 重置状态
  public reset(): void {
    this.loadingStatus = LoadingStatus.LOADING;
    this.errorMessage = '';
    this.hasMore = true;
    this.isLoadingMore = false;
    this.currentPage = 1;
    this.totalCount = 0;
  }

  // 更新分页信息
  public updatePagination(currentPage: number, totalCount: number, hasMore: boolean): void {
    this.currentPage = currentPage;
    this.totalCount = totalCount;
    this.hasMore = hasMore;
  }

  // 检查是否可以加载更多
  public canLoadMore(): boolean {
    return this.hasMore && !this.isLoadingMore && this.loadingStatus === LoadingStatus.SUCCESS;
  }

  // 检查是否为加载中状态
  public isLoading(): boolean {
    return this.loadingStatus === LoadingStatus.LOADING;
  }

  // 检查是否为成功状态
  public isSuccess(): boolean {
    return this.loadingStatus === LoadingStatus.SUCCESS;
  }

  // 检查是否为失败状态
  public isFailed(): boolean {
    return this.loadingStatus === LoadingStatus.FAILED;
  }

  // 检查是否为空数据状态
  public isEmpty(): boolean {
    return this.loadingStatus === LoadingStatus.EMPTY;
  }
}

// 加载状态枚举
export enum LoadingStatus {
  LOADING = 'loading',    // 加载中
  SUCCESS = 'success',    // 加载成功
  FAILED = 'failed',      // 加载失败
  EMPTY = 'empty'         // 空数据
}
```

**使用示例**：
```typescript
// 创建加载模型
@State loadingModel: LoadingModel = new LoadingModel();

// 开始加载数据
private async loadData(): Promise<void> {
  this.loadingModel.setLoading();

  try {
    const response = await this.dataService.getData();

    if (response.data.length === 0) {
      this.loadingModel.setEmpty();
    } else {
      this.loadingModel.setSuccess();
      this.loadingModel.updatePagination(
        response.currentPage,
        response.totalCount,
        response.hasMore
      );
    }
  } catch (error) {
    this.loadingModel.setError(error.message);
  }
}

// 加载更多数据
private async loadMoreData(): Promise<void> {
  if (!this.loadingModel.canLoadMore()) {
    return;
  }

  this.loadingModel.startLoadingMore();

  try {
    const response = await this.dataService.getMoreData(this.loadingModel.currentPage + 1);

    // 追加数据到列表
    this.dataList.push(...response.data);

    this.loadingModel.updatePagination(
      response.currentPage,
      response.totalCount,
      response.hasMore
    );
    this.loadingModel.stopLoadingMore(response.hasMore);
  } catch (error) {
    this.loadingModel.stopLoadingMore();
    // 显示错误提示
    this.showErrorToast(error.message);
  }
}

// 在UI中使用
build() {
  Column() {
    if (this.loadingModel.isLoading()) {
      LoadingView()
    } else if (this.loadingModel.isFailed()) {
      LoadingFailedView({
        message: this.loadingModel.errorMessage,
        onRetry: () => this.loadData()
      })
    } else if (this.loadingModel.isEmpty()) {
      EmptyContentView({ message: '暂无数据' })
    } else {
      // 数据列表
      List() {
        ForEach(this.dataList, (item) => {
          ListItem() {
            // 列表项内容
          }
        })

        // 加载更多组件
        if (this.loadingModel.hasMore) {
          ListItem() {
            LoadingMore({ isLoading: this.loadingModel.isLoadingMore })
          }
          .onClick(() => this.loadMoreData())
        } else {
          ListItem() {
            NoMore()
          }
        }
      }
    }
  }
}
```

#### ResponseData - 响应数据模型
**文件位置**：`src/main/ets/model/ResponseData.ets`

**完整实现**：
```typescript
// 通用响应数据模型
export class ResponseData<T> {
  // 状态码
  public code: number = 0;
  // 响应消息
  public message: string = '';
  // 响应数据
  public data: T;
  // 时间戳
  public timestamp: number = Date.now();
  // 请求ID
  public requestId?: string;
  // 分页信息
  public pagination?: PaginationInfo;

  constructor(data: T, code: number = 200, message: string = 'success') {
    this.data = data;
    this.code = code;
    this.message = message;
  }

  // 检查是否成功
  public isSuccess(): boolean {
    return this.code >= 200 && this.code < 300;
  }

  // 检查是否失败
  public isFailed(): boolean {
    return !this.isSuccess();
  }

  // 获取错误信息
  public getErrorMessage(): string {
    return this.isSuccess() ? '' : this.message;
  }

  // 创建成功响应
  public static success<T>(data: T, message: string = 'success'): ResponseData<T> {
    return new ResponseData(data, 200, message);
  }

  // 创建失败响应
  public static error<T>(code: number, message: string, data?: T): ResponseData<T> {
    const response = new ResponseData(data as T, code, message);
    return response;
  }

  // 创建分页响应
  public static paginated<T>(
    data: T,
    pagination: PaginationInfo,
    message: string = 'success'
  ): ResponseData<T> {
    const response = new ResponseData(data, 200, message);
    response.pagination = pagination;
    return response;
  }
}

// 分页信息接口
export interface PaginationInfo {
  currentPage: number;    // 当前页码
  pageSize: number;       // 每页数据量
  totalCount: number;     // 总数据量
  totalPages: number;     // 总页数
  hasMore: boolean;       // 是否有更多数据
  hasPrevious: boolean;   // 是否有上一页
}

// 列表响应数据模型
export class ListResponseData<T> extends ResponseData<T[]> {
  constructor(
    data: T[],
    pagination: PaginationInfo,
    code: number = 200,
    message: string = 'success'
  ) {
    super(data, code, message);
    this.pagination = pagination;
  }

  // 获取列表数据
  public getList(): T[] {
    return this.data || [];
  }

  // 检查是否为空列表
  public isEmpty(): boolean {
    return this.getList().length === 0;
  }

  // 获取数据数量
  public getCount(): number {
    return this.getList().length;
  }
}

// 结果数据接口（用于MockRequest）
export interface ResultData<T> {
  code: number;
  message: string;
  data: T;
  timestamp?: number;
}
```

**使用示例**：
```typescript
// 在服务层使用
export class DataService {
  async getData(): Promise<ResponseData<DataItem[]>> {
    try {
      const response = await http.request({
        method: http.RequestMethod.GET,
        url: '/api/data'
      });

      if (response.responseCode === 200) {
        const result = JSON.parse(response.result as string);
        return ResponseData.success(result.data, result.message);
      } else {
        return ResponseData.error(response.responseCode, '请求失败');
      }
    } catch (error) {
      return ResponseData.error(500, error.message);
    }
  }

  async getPagedData(page: number, size: number): Promise<ListResponseData<DataItem>> {
    try {
      const response = await http.request({
        method: http.RequestMethod.GET,
        url: `/api/data?page=${page}&size=${size}`
      });

      if (response.responseCode === 200) {
        const result = JSON.parse(response.result as string);
        const pagination: PaginationInfo = {
          currentPage: result.currentPage,
          pageSize: result.pageSize,
          totalCount: result.totalCount,
          totalPages: Math.ceil(result.totalCount / result.pageSize),
          hasMore: result.currentPage < Math.ceil(result.totalCount / result.pageSize),
          hasPrevious: result.currentPage > 1
        };

        return new ListResponseData(result.data, pagination);
      } else {
        return new ListResponseData([], {
          currentPage: page,
          pageSize: size,
          totalCount: 0,
          totalPages: 0,
          hasMore: false,
          hasPrevious: false
        }, response.responseCode, '请求失败');
      }
    } catch (error) {
      return new ListResponseData([], {
        currentPage: page,
        pageSize: size,
        totalCount: 0,
        totalPages: 0,
        hasMore: false,
        hasPrevious: false
      }, 500, error.message);
    }
  }
}

// 在ViewModel中使用
export class DataViewModel {
  @State dataList: DataItem[] = [];
  @State loadingModel: LoadingModel = new LoadingModel();

  async loadData(): Promise<void> {
    this.loadingModel.setLoading();

    const response = await this.dataService.getData();

    if (response.isSuccess()) {
      this.dataList = response.data;
      this.loadingModel.setSuccess();
    } else {
      this.loadingModel.setError(response.getErrorMessage());
    }
  }

  async loadPagedData(page: number): Promise<void> {
    const response = await this.dataService.getPagedData(page, 20);

    if (response.isSuccess()) {
      if (page === 1) {
        this.dataList = response.getList();
      } else {
        this.dataList.push(...response.getList());
      }

      if (response.pagination) {
        this.loadingModel.updatePagination(
          response.pagination.currentPage,
          response.pagination.totalCount,
          response.pagination.hasMore
        );
      }
    } else {
      this.loadingModel.setError(response.getErrorMessage());
    }
  }
}
```

### 存储管理详解

#### MockRequest - 模拟请求类
**文件位置**：`src/main/ets/storagemanager/MockRequest.ets`

**完整实现**：
```typescript
import { util } from '@kit.ArkTS';
import { BusinessError } from '@kit.BasicServicesKit';
import Logger from '../util/Logger';

const TAG = '[MockRequest]';

class MockRequest {
  // 调用模拟数据请求
  public call<T>(trigger: string): Promise<T> {
    return new Promise((resolve: (value: T | PromiseLike<T>) => void,
      reject: ((reason?: BusinessError) => void)) => {
      try {
        const context: Context = getContext();
        // 从rawfile/mockdata目录读取JSON文件
        const result: Uint8Array = context.resourceManager.getRawFileContentSync(`mockdata/${trigger}.json`);

        // 解码为字符串
        const textDecoder = util.TextDecoder.create('utf-8', { ignoreBOM: true });
        const content: string = textDecoder.decodeToString(result, { stream: false });

        // 解析JSON数据
        const jsonContent: ResultData<T> = JSON.parse(content) as ResultData<T>;

        Logger.info(TAG, `Successfully loaded mock data: ${trigger}`);
        resolve(jsonContent.data);
      } catch (error) {
        Logger.error(TAG, `Failed to load mock data: ${trigger}, error: ${error.message}`);
        reject(error);
      }
    });
  }

  // 异步调用模拟数据（带延迟）
  public async callWithDelay<T>(trigger: string, delay: number = 1000): Promise<T> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, delay));
    return this.call<T>(trigger);
  }

  // 批量调用模拟数据
  public async callBatch<T>(triggers: string[]): Promise<T[]> {
    const promises = triggers.map(trigger => this.call<T>(trigger));
    return Promise.all(promises);
  }

  // 检查模拟数据文件是否存在
  public checkFileExists(trigger: string): boolean {
    try {
      const context: Context = getContext();
      context.resourceManager.getRawFileContentSync(`mockdata/${trigger}.json`);
      return true;
    } catch (error) {
      return false;
    }
  }

  // 获取所有可用的模拟数据文件
  public async getAvailableFiles(): Promise<string[]> {
    try {
      const context: Context = getContext();
      const files = await context.resourceManager.getRawFileList('mockdata');
      return files.filter(file => file.endsWith('.json')).map(file => file.replace('.json', ''));
    } catch (error) {
      Logger.error(TAG, `Failed to get available mock files: ${error.message}`);
      return [];
    }
  }
}

// 创建单例实例
const mockRequest = new MockRequest();
export default mockRequest;

// 结果数据接口
interface ResultData<T> {
  code: number;
  message: string;
  data: T;
  timestamp?: number;
}
```

**使用示例**：
```typescript
// 基础用法
import MockRequest from '@ohos/common';

// 加载用户数据
const userData = await MockRequest.call<UserData>('user');

// 带延迟的模拟请求
const productData = await MockRequest.callWithDelay<ProductData>('products', 2000);

// 批量加载数据
const [users, products, orders] = await MockRequest.callBatch<any>(['users', 'products', 'orders']);

// 检查文件是否存在
if (MockRequest.checkFileExists('config')) {
  const config = await MockRequest.call<ConfigData>('config');
}

// 获取所有可用的模拟数据文件
const availableFiles = await MockRequest.getAvailableFiles();
console.log('可用的模拟数据文件:', availableFiles);

// 在服务类中使用
export class UserService {
  async getUserList(): Promise<ResponseData<User[]>> {
    try {
      const users = await MockRequest.call<User[]>('userList');
      return ResponseData.success(users);
    } catch (error) {
      return ResponseData.error(500, '获取用户列表失败');
    }
  }

  async getUserDetail(userId: string): Promise<ResponseData<User>> {
    try {
      const user = await MockRequest.call<User>(`user_${userId}`);
      return ResponseData.success(user);
    } catch (error) {
      return ResponseData.error(404, '用户不存在');
    }
  }
}
```

**模拟数据文件示例**：
```json
// src/main/resources/rawfile/mockdata/userList.json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "1",
      "name": "张三",
      "email": "<EMAIL>",
      "avatar": "https://example.com/avatar1.jpg"
    },
    {
      "id": "2",
      "name": "李四",
      "email": "<EMAIL>",
      "avatar": "https://example.com/avatar2.jpg"
    }
  ],
  "timestamp": 1640995200000
}
```

#### PreferenceManager - 偏好设置管理器
**文件位置**：`src/main/ets/storagemanager/PreferenceManager.ets`

**完整实现**：
```typescript
import { preferences } from '@kit.ArkData';
import { BusinessError } from '@kit.BasicServicesKit';
import Logger from '../util/Logger';

const TAG = '[PreferenceManager]';

export class PreferenceManager {
  private static instance: PreferenceManager;
  private preferencesMap: Map<string, preferences.Preferences> = new Map();

  private constructor() {}

  public static getInstance(): PreferenceManager {
    if (!PreferenceManager.instance) {
      PreferenceManager.instance = new PreferenceManager();
    }
    return PreferenceManager.instance;
  }

  // 获取偏好设置实例
  private async getPreferences(name: string): Promise<preferences.Preferences> {
    if (this.preferencesMap.has(name)) {
      return this.preferencesMap.get(name)!;
    }

    try {
      const context = getContext();
      const prefs = await preferences.getPreferences(context, name);
      this.preferencesMap.set(name, prefs);
      return prefs;
    } catch (error) {
      Logger.error(TAG, `Failed to get preferences: ${name}, error: ${error.message}`);
      throw error;
    }
  }

  // 设置字符串值
  public async setString(name: string, key: string, value: string): Promise<void> {
    try {
      const prefs = await this.getPreferences(name);
      await prefs.put(key, value);
      await prefs.flush();
      Logger.info(TAG, `Set string value: ${name}.${key} = ${value}`);
    } catch (error) {
      Logger.error(TAG, `Failed to set string: ${name}.${key}, error: ${error.message}`);
      throw error;
    }
  }

  // 获取字符串值
  public async getString(name: string, key: string, defaultValue: string = ''): Promise<string> {
    try {
      const prefs = await this.getPreferences(name);
      const value = await prefs.get(key, defaultValue);
      return value as string;
    } catch (error) {
      Logger.error(TAG, `Failed to get string: ${name}.${key}, error: ${error.message}`);
      return defaultValue;
    }
  }

  // 设置数字值
  public async setNumber(name: string, key: string, value: number): Promise<void> {
    try {
      const prefs = await this.getPreferences(name);
      await prefs.put(key, value);
      await prefs.flush();
      Logger.info(TAG, `Set number value: ${name}.${key} = ${value}`);
    } catch (error) {
      Logger.error(TAG, `Failed to set number: ${name}.${key}, error: ${error.message}`);
      throw error;
    }
  }

  // 获取数字值
  public async getNumber(name: string, key: string, defaultValue: number = 0): Promise<number> {
    try {
      const prefs = await this.getPreferences(name);
      const value = await prefs.get(key, defaultValue);
      return value as number;
    } catch (error) {
      Logger.error(TAG, `Failed to get number: ${name}.${key}, error: ${error.message}`);
      return defaultValue;
    }
  }

  // 设置布尔值
  public async setBoolean(name: string, key: string, value: boolean): Promise<void> {
    try {
      const prefs = await this.getPreferences(name);
      await prefs.put(key, value);
      await prefs.flush();
      Logger.info(TAG, `Set boolean value: ${name}.${key} = ${value}`);
    } catch (error) {
      Logger.error(TAG, `Failed to set boolean: ${name}.${key}, error: ${error.message}`);
      throw error;
    }
  }

  // 获取布尔值
  public async getBoolean(name: string, key: string, defaultValue: boolean = false): Promise<boolean> {
    try {
      const prefs = await this.getPreferences(name);
      const value = await prefs.get(key, defaultValue);
      return value as boolean;
    } catch (error) {
      Logger.error(TAG, `Failed to get boolean: ${name}.${key}, error: ${error.message}`);
      return defaultValue;
    }
  }

  // 设置对象值（JSON序列化）
  public async setObject<T>(name: string, key: string, value: T): Promise<void> {
    try {
      const jsonString = JSON.stringify(value);
      await this.setString(name, key, jsonString);
      Logger.info(TAG, `Set object value: ${name}.${key}`);
    } catch (error) {
      Logger.error(TAG, `Failed to set object: ${name}.${key}, error: ${error.message}`);
      throw error;
    }
  }

  // 获取对象值（JSON反序列化）
  public async getObject<T>(name: string, key: string, defaultValue?: T): Promise<T | undefined> {
    try {
      const jsonString = await this.getString(name, key);
      if (jsonString) {
        return JSON.parse(jsonString) as T;
      }
      return defaultValue;
    } catch (error) {
      Logger.error(TAG, `Failed to get object: ${name}.${key}, error: ${error.message}`);
      return defaultValue;
    }
  }

  // 删除键值
  public async delete(name: string, key: string): Promise<void> {
    try {
      const prefs = await this.getPreferences(name);
      await prefs.delete(key);
      await prefs.flush();
      Logger.info(TAG, `Deleted key: ${name}.${key}`);
    } catch (error) {
      Logger.error(TAG, `Failed to delete key: ${name}.${key}, error: ${error.message}`);
      throw error;
    }
  }

  // 清空所有数据
  public async clear(name: string): Promise<void> {
    try {
      const prefs = await this.getPreferences(name);
      await prefs.clear();
      await prefs.flush();
      Logger.info(TAG, `Cleared all data: ${name}`);
    } catch (error) {
      Logger.error(TAG, `Failed to clear data: ${name}, error: ${error.message}`);
      throw error;
    }
  }

  // 检查键是否存在
  public async has(name: string, key: string): Promise<boolean> {
    try {
      const prefs = await this.getPreferences(name);
      return await prefs.has(key);
    } catch (error) {
      Logger.error(TAG, `Failed to check key existence: ${name}.${key}, error: ${error.message}`);
      return false;
    }
  }

  // 获取所有键
  public async getAllKeys(name: string): Promise<string[]> {
    try {
      const prefs = await this.getPreferences(name);
      return await prefs.getAll() as string[];
    } catch (error) {
      Logger.error(TAG, `Failed to get all keys: ${name}, error: ${error.message}`);
      return [];
    }
  }

  // 批量设置
  public async setBatch(name: string, data: Record<string, preferences.ValueType>): Promise<void> {
    try {
      const prefs = await this.getPreferences(name);

      for (const [key, value] of Object.entries(data)) {
        await prefs.put(key, value);
      }

      await prefs.flush();
      Logger.info(TAG, `Batch set completed: ${name}, keys: ${Object.keys(data).join(', ')}`);
    } catch (error) {
      Logger.error(TAG, `Failed to batch set: ${name}, error: ${error.message}`);
      throw error;
    }
  }

  // 监听数据变化
  public async on(name: string, callback: (key: string) => void): Promise<void> {
    try {
      const prefs = await this.getPreferences(name);
      prefs.on('change', callback);
      Logger.info(TAG, `Registered change listener: ${name}`);
    } catch (error) {
      Logger.error(TAG, `Failed to register listener: ${name}, error: ${error.message}`);
      throw error;
    }
  }

  // 取消监听
  public async off(name: string, callback?: (key: string) => void): Promise<void> {
    try {
      const prefs = await this.getPreferences(name);
      prefs.off('change', callback);
      Logger.info(TAG, `Unregistered change listener: ${name}`);
    } catch (error) {
      Logger.error(TAG, `Failed to unregister listener: ${name}, error: ${error.message}`);
      throw error;
    }
  }
}

// 常用偏好设置名称常量
export class PreferenceNames {
  public static readonly USER_SETTINGS = 'user_settings';
  public static readonly APP_CONFIG = 'app_config';
  public static readonly CACHE_DATA = 'cache_data';
  public static readonly USER_PREFERENCES = 'user_preferences';
}

// 常用设置键名常量
export class SettingKeys {
  public static readonly THEME_MODE = 'theme_mode';
  public static readonly LANGUAGE = 'language';
  public static readonly FONT_SIZE = 'font_size';
  public static readonly NOTIFICATION_ENABLED = 'notification_enabled';
  public static readonly AUTO_UPDATE = 'auto_update';
  public static readonly LAST_LOGIN_TIME = 'last_login_time';
  public static readonly USER_TOKEN = 'user_token';
  public static readonly FIRST_LAUNCH = 'first_launch';
}
```

**使用示例**：
```typescript
import { PreferenceManager, PreferenceNames, SettingKeys } from '@ohos/common';

// 获取偏好设置管理器实例
const preferenceManager = PreferenceManager.getInstance();

// 用户设置管理
export class UserSettingsManager {
  // 保存主题模式
  async saveThemeMode(mode: string): Promise<void> {
    await preferenceManager.setString(PreferenceNames.USER_SETTINGS, SettingKeys.THEME_MODE, mode);
  }

  // 获取主题模式
  async getThemeMode(): Promise<string> {
    return await preferenceManager.getString(PreferenceNames.USER_SETTINGS, SettingKeys.THEME_MODE, 'auto');
  }

  // 保存用户偏好
  async saveUserPreferences(preferences: UserPreferences): Promise<void> {
    await preferenceManager.setObject(PreferenceNames.USER_PREFERENCES, 'preferences', preferences);
  }

  // 获取用户偏好
  async getUserPreferences(): Promise<UserPreferences | undefined> {
    return await preferenceManager.getObject<UserPreferences>(PreferenceNames.USER_PREFERENCES, 'preferences');
  }

  // 保存登录状态
  async saveLoginInfo(token: string, userId: string): Promise<void> {
    const loginData = {
      [SettingKeys.USER_TOKEN]: token,
      'user_id': userId,
      [SettingKeys.LAST_LOGIN_TIME]: Date.now()
    };

    await preferenceManager.setBatch(PreferenceNames.USER_SETTINGS, loginData);
  }

  // 清除登录信息
  async clearLoginInfo(): Promise<void> {
    await preferenceManager.delete(PreferenceNames.USER_SETTINGS, SettingKeys.USER_TOKEN);
    await preferenceManager.delete(PreferenceNames.USER_SETTINGS, 'user_id');
  }

  // 检查是否首次启动
  async isFirstLaunch(): Promise<boolean> {
    const isFirst = await preferenceManager.getBoolean(PreferenceNames.APP_CONFIG, SettingKeys.FIRST_LAUNCH, true);
    if (isFirst) {
      await preferenceManager.setBoolean(PreferenceNames.APP_CONFIG, SettingKeys.FIRST_LAUNCH, false);
    }
    return isFirst;
  }

  // 监听设置变化
  async watchSettingsChange(callback: (key: string) => void): Promise<void> {
    await preferenceManager.on(PreferenceNames.USER_SETTINGS, callback);
  }
}

// 缓存管理
export class CacheManager {
  // 缓存数据
  async cacheData<T>(key: string, data: T, expireTime?: number): Promise<void> {
    const cacheItem = {
      data: data,
      timestamp: Date.now(),
      expireTime: expireTime
    };

    await preferenceManager.setObject(PreferenceNames.CACHE_DATA, key, cacheItem);
  }

  // 获取缓存数据
  async getCachedData<T>(key: string): Promise<T | null> {
    const cacheItem = await preferenceManager.getObject<any>(PreferenceNames.CACHE_DATA, key);

    if (!cacheItem) {
      return null;
    }

    // 检查是否过期
    if (cacheItem.expireTime && Date.now() > cacheItem.expireTime) {
      await preferenceManager.delete(PreferenceNames.CACHE_DATA, key);
      return null;
    }

    return cacheItem.data as T;
  }

  // 清除过期缓存
  async clearExpiredCache(): Promise<void> {
    const allKeys = await preferenceManager.getAllKeys(PreferenceNames.CACHE_DATA);
    const now = Date.now();

    for (const key of allKeys) {
      const cacheItem = await preferenceManager.getObject<any>(PreferenceNames.CACHE_DATA, key);
      if (cacheItem && cacheItem.expireTime && now > cacheItem.expireTime) {
        await preferenceManager.delete(PreferenceNames.CACHE_DATA, key);
      }
    }
  }
}

// 数据类型定义
interface UserPreferences {
  fontSize: number;
  language: string;
  notificationEnabled: boolean;
  autoUpdate: boolean;
  theme: string;
}
```

### 视图模型架构详解

#### BaseVM - 基础视图模型
**文件位置**：`src/main/ets/viewmodel/BaseVM.ets`

**完整实现**：
```typescript
import { BaseState } from './BaseState';
import { BaseVMEvent } from './BaseVMEvent';

// 基础视图模型抽象类
export abstract class BaseVM<T extends BaseState> {
  protected state: T;
  private listeners: Array<(state: T) => void> = [];

  public constructor(initialState: T) {
    this.state = initialState;
  }

  // 获取当前状态
  getState(): T {
    return this.state;
  }

  // 更新状态
  protected updateState(newState: Partial<T>): void {
    this.state = { ...this.state, ...newState };
    this.notifyStateChange();
  }

  // 设置完整状态
  protected setState(newState: T): void {
    this.state = newState;
    this.notifyStateChange();
  }

  // 添加状态变化监听器
  public addStateListener(listener: (state: T) => void): void {
    this.listeners.push(listener);
  }

  // 移除状态变化监听器
  public removeStateListener(listener: (state: T) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  // 通知状态变化
  private notifyStateChange(): void {
    this.listeners.forEach(listener => {
      listener(this.state);
    });
  }

  // 发送事件抽象方法
  public abstract sendEvent(baseVMEvent: BaseVMEvent): void;

  // 生命周期方法
  public onCreate(): void {
    // 子类可重写
  }

  public onDestroy(): void {
    // 清理监听器
    this.listeners = [];
  }

  // 异步操作包装器
  protected async executeAsync<R>(
    operation: () => Promise<R>,
    loadingStateKey?: keyof T,
    errorStateKey?: keyof T
  ): Promise<R | null> {
    try {
      // 设置加载状态
      if (loadingStateKey) {
        this.updateState({ [loadingStateKey]: true } as Partial<T>);
      }

      // 清除之前的错误
      if (errorStateKey) {
        this.updateState({ [errorStateKey]: null } as Partial<T>);
      }

      const result = await operation();

      // 清除加载状态
      if (loadingStateKey) {
        this.updateState({ [loadingStateKey]: false } as Partial<T>);
      }

      return result;
    } catch (error) {
      // 设置错误状态
      if (errorStateKey) {
        this.updateState({ [errorStateKey]: error.message } as Partial<T>);
      }

      // 清除加载状态
      if (loadingStateKey) {
        this.updateState({ [loadingStateKey]: false } as Partial<T>);
      }

      return null;
    }
  }

  // 防抖执行
  protected debounce<Args extends any[]>(
    func: (...args: Args) => void,
    delay: number
  ): (...args: Args) => void {
    let timeoutId: number;

    return (...args: Args) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  }

  // 节流执行
  protected throttle<Args extends any[]>(
    func: (...args: Args) => void,
    delay: number
  ): (...args: Args) => void {
    let lastExecTime = 0;

    return (...args: Args) => {
      const currentTime = Date.now();
      if (currentTime - lastExecTime >= delay) {
        func.apply(this, args);
        lastExecTime = currentTime;
      }
    };
  }
}

// 扩展的基础视图模型（带加载状态管理）
export abstract class BaseLoadingVM<T extends BaseState> extends BaseVM<T> {
  protected loadingModel: LoadingModel = new LoadingModel();

  // 获取加载模型
  public getLoadingModel(): LoadingModel {
    return this.loadingModel;
  }

  // 执行带加载状态的异步操作
  protected async executeWithLoading<R>(
    operation: () => Promise<R>,
    onSuccess?: (result: R) => void,
    onError?: (error: Error) => void
  ): Promise<R | null> {
    this.loadingModel.setLoading();

    try {
      const result = await operation();
      this.loadingModel.setSuccess();

      if (onSuccess) {
        onSuccess(result);
      }

      return result;
    } catch (error) {
      this.loadingModel.setError(error.message);

      if (onError) {
        onError(error);
      }

      return null;
    }
  }

  // 执行分页加载
  protected async executePagedLoading<R>(
    operation: (page: number, size: number) => Promise<ListResponseData<R>>,
    page: number = 1,
    size: number = 20,
    isLoadMore: boolean = false
  ): Promise<R[]> {
    if (isLoadMore) {
      this.loadingModel.startLoadingMore();
    } else {
      this.loadingModel.setLoading();
    }

    try {
      const response = await operation(page, size);

      if (response.isSuccess()) {
        const data = response.getList();

        if (response.pagination) {
          this.loadingModel.updatePagination(
            response.pagination.currentPage,
            response.pagination.totalCount,
            response.pagination.hasMore
          );
        }

        if (isLoadMore) {
          this.loadingModel.stopLoadingMore(response.pagination?.hasMore ?? false);
        } else {
          this.loadingModel.setSuccess();
        }

        return data;
      } else {
        throw new Error(response.getErrorMessage());
      }
    } catch (error) {
      if (isLoadMore) {
        this.loadingModel.stopLoadingMore();
      } else {
        this.loadingModel.setError(error.message);
      }

      return [];
    }
  }
}
```

#### BaseState - 基础状态类
**文件位置**：`src/main/ets/viewmodel/BaseState.ets`

**完整实现**：
```typescript
// 基础状态抽象类
@Observed
export abstract class BaseState {
  // 状态版本号，用于追踪状态变化
  public version: number = 0;
  // 创建时间戳
  public createdAt: number = Date.now();
  // 最后更新时间戳
  public updatedAt: number = Date.now();

  // 更新状态时间戳
  public updateTimestamp(): void {
    this.updatedAt = Date.now();
    this.version++;
  }

  // 克隆状态
  public clone(): this {
    const cloned = Object.create(Object.getPrototypeOf(this));
    return Object.assign(cloned, this);
  }

  // 重置状态（子类实现）
  public abstract reset(): void;

  // 验证状态（子类实现）
  public abstract validate(): boolean;

  // 获取状态摘要
  public getSummary(): StateSummary {
    return {
      version: this.version,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      isValid: this.validate()
    };
  }
}

// 状态摘要接口
export interface StateSummary {
  version: number;
  createdAt: number;
  updatedAt: number;
  isValid: boolean;
}

// 通用页面状态类
@Observed
export class PageState extends BaseState {
  // 页面标题
  public title: string = '';
  // 是否显示加载指示器
  public isLoading: boolean = false;
  // 错误信息
  public errorMessage: string = '';
  // 是否显示返回按钮
  public showBackButton: boolean = true;
  // 页面参数
  public params: Record<string, any> = {};

  public reset(): void {
    this.title = '';
    this.isLoading = false;
    this.errorMessage = '';
    this.showBackButton = true;
    this.params = {};
    this.updateTimestamp();
  }

  public validate(): boolean {
    return this.title.length > 0;
  }

  // 设置页面标题
  public setTitle(title: string): void {
    this.title = title;
    this.updateTimestamp();
  }

  // 设置加载状态
  public setLoading(loading: boolean): void {
    this.isLoading = loading;
    this.updateTimestamp();
  }

  // 设置错误信息
  public setError(error: string): void {
    this.errorMessage = error;
    this.isLoading = false;
    this.updateTimestamp();
  }

  // 清除错误
  public clearError(): void {
    this.errorMessage = '';
    this.updateTimestamp();
  }

  // 设置页面参数
  public setParams(params: Record<string, any>): void {
    this.params = { ...this.params, ...params };
    this.updateTimestamp();
  }
}

// 列表状态类
@Observed
export class ListState<T> extends BaseState {
  // 列表数据
  public items: T[] = [];
  // 选中的项目
  public selectedItems: T[] = [];
  // 当前页码
  public currentPage: number = 1;
  // 每页数据量
  public pageSize: number = 20;
  // 总数据量
  public totalCount: number = 0;
  // 是否有更多数据
  public hasMore: boolean = true;
  // 是否正在加载更多
  public isLoadingMore: boolean = false;
  // 搜索关键词
  public searchKeyword: string = '';
  // 排序字段
  public sortField: string = '';
  // 排序方向
  public sortDirection: 'asc' | 'desc' = 'asc';

  public reset(): void {
    this.items = [];
    this.selectedItems = [];
    this.currentPage = 1;
    this.pageSize = 20;
    this.totalCount = 0;
    this.hasMore = true;
    this.isLoadingMore = false;
    this.searchKeyword = '';
    this.sortField = '';
    this.sortDirection = 'asc';
    this.updateTimestamp();
  }

  public validate(): boolean {
    return this.currentPage > 0 && this.pageSize > 0;
  }

  // 设置列表数据
  public setItems(items: T[]): void {
    this.items = items;
    this.updateTimestamp();
  }

  // 追加列表数据
  public appendItems(items: T[]): void {
    this.items.push(...items);
    this.updateTimestamp();
  }

  // 更新分页信息
  public updatePagination(currentPage: number, totalCount: number, hasMore: boolean): void {
    this.currentPage = currentPage;
    this.totalCount = totalCount;
    this.hasMore = hasMore;
    this.updateTimestamp();
  }

  // 设置搜索关键词
  public setSearchKeyword(keyword: string): void {
    this.searchKeyword = keyword;
    this.currentPage = 1; // 重置页码
    this.updateTimestamp();
  }

  // 设置排序
  public setSort(field: string, direction: 'asc' | 'desc'): void {
    this.sortField = field;
    this.sortDirection = direction;
    this.currentPage = 1; // 重置页码
    this.updateTimestamp();
  }

  // 选中项目
  public selectItem(item: T): void {
    if (!this.selectedItems.includes(item)) {
      this.selectedItems.push(item);
      this.updateTimestamp();
    }
  }

  // 取消选中项目
  public deselectItem(item: T): void {
    const index = this.selectedItems.indexOf(item);
    if (index > -1) {
      this.selectedItems.splice(index, 1);
      this.updateTimestamp();
    }
  }

  // 清空选中
  public clearSelection(): void {
    this.selectedItems = [];
    this.updateTimestamp();
  }

  // 全选
  public selectAll(): void {
    this.selectedItems = [...this.items];
    this.updateTimestamp();
  }

  // 检查是否可以加载更多
  public canLoadMore(): boolean {
    return this.hasMore && !this.isLoadingMore;
  }
}
```

#### BaseVMEvent - 基础视图模型事件
**文件位置**：`src/main/ets/viewmodel/BaseVMEvent.ets`

**完整实现**：
```typescript
// 基础视图模型事件抽象类
export abstract class BaseVMEvent {
  // 事件类型
  public readonly type: string;
  // 事件时间戳
  public readonly timestamp: number;
  // 事件数据
  public readonly data?: any;

  constructor(type: string, data?: any) {
    this.type = type;
    this.timestamp = Date.now();
    this.data = data;
  }

  // 获取事件描述
  public abstract getDescription(): string;
}

// 通用事件类型常量
export class EventTypes {
  // 页面事件
  public static readonly PAGE_LOAD = 'page_load';
  public static readonly PAGE_REFRESH = 'page_refresh';
  public static readonly PAGE_BACK = 'page_back';

  // 数据事件
  public static readonly DATA_LOAD = 'data_load';
  public static readonly DATA_REFRESH = 'data_refresh';
  public static readonly DATA_LOAD_MORE = 'data_load_more';
  public static readonly DATA_SEARCH = 'data_search';
  public static readonly DATA_SORT = 'data_sort';

  // 用户交互事件
  public static readonly USER_CLICK = 'user_click';
  public static readonly USER_INPUT = 'user_input';
  public static readonly USER_SELECT = 'user_select';
  public static readonly USER_SCROLL = 'user_scroll';

  // 状态变化事件
  public static readonly STATE_CHANGE = 'state_change';
  public static readonly LOADING_START = 'loading_start';
  public static readonly LOADING_END = 'loading_end';
  public static readonly ERROR_OCCURRED = 'error_occurred';
}

// 页面事件类
export class PageEvent extends BaseVMEvent {
  constructor(type: string, data?: any) {
    super(type, data);
  }

  public getDescription(): string {
    switch (this.type) {
      case EventTypes.PAGE_LOAD:
        return '页面加载事件';
      case EventTypes.PAGE_REFRESH:
        return '页面刷新事件';
      case EventTypes.PAGE_BACK:
        return '页面返回事件';
      default:
        return `页面事件: ${this.type}`;
    }
  }
}

// 数据事件类
export class DataEvent extends BaseVMEvent {
  constructor(type: string, data?: any) {
    super(type, data);
  }

  public getDescription(): string {
    switch (this.type) {
      case EventTypes.DATA_LOAD:
        return '数据加载事件';
      case EventTypes.DATA_REFRESH:
        return '数据刷新事件';
      case EventTypes.DATA_LOAD_MORE:
        return '加载更多数据事件';
      case EventTypes.DATA_SEARCH:
        return `数据搜索事件: ${this.data?.keyword || ''}`;
      case EventTypes.DATA_SORT:
        return `数据排序事件: ${this.data?.field || ''} ${this.data?.direction || ''}`;
      default:
        return `数据事件: ${this.type}`;
    }
  }
}

// 用户交互事件类
export class UserEvent extends BaseVMEvent {
  constructor(type: string, data?: any) {
    super(type, data);
  }

  public getDescription(): string {
    switch (this.type) {
      case EventTypes.USER_CLICK:
        return `用户点击事件: ${this.data?.target || ''}`;
      case EventTypes.USER_INPUT:
        return `用户输入事件: ${this.data?.field || ''}`;
      case EventTypes.USER_SELECT:
        return `用户选择事件: ${this.data?.item || ''}`;
      case EventTypes.USER_SCROLL:
        return '用户滚动事件';
      default:
        return `用户交互事件: ${this.type}`;
    }
  }
}

// 状态变化事件类
export class StateEvent extends BaseVMEvent {
  constructor(type: string, data?: any) {
    super(type, data);
  }

  public getDescription(): string {
    switch (this.type) {
      case EventTypes.STATE_CHANGE:
        return '状态变化事件';
      case EventTypes.LOADING_START:
        return '开始加载事件';
      case EventTypes.LOADING_END:
        return '结束加载事件';
      case EventTypes.ERROR_OCCURRED:
        return `错误发生事件: ${this.data?.message || ''}`;
      default:
        return `状态事件: ${this.type}`;
    }
  }
}
```

**MVVM架构使用示例**：
```typescript
// 1. 定义具体的状态类
@Observed
class UserListState extends ListState<User> {
  public filterType: string = 'all';

  public reset(): void {
    super.reset();
    this.filterType = 'all';
  }

  public setFilterType(type: string): void {
    this.filterType = type;
    this.currentPage = 1;
    this.updateTimestamp();
  }
}

// 2. 定义具体的视图模型
class UserListViewModel extends BaseLoadingVM<UserListState> {
  private userService: UserService;

  constructor() {
    super(new UserListState());
    this.userService = new UserService();
  }

  // 处理事件
  public sendEvent(event: BaseVMEvent): void {
    switch (event.type) {
      case EventTypes.DATA_LOAD:
        this.loadUsers();
        break;
      case EventTypes.DATA_REFRESH:
        this.refreshUsers();
        break;
      case EventTypes.DATA_LOAD_MORE:
        this.loadMoreUsers();
        break;
      case EventTypes.DATA_SEARCH:
        this.searchUsers(event.data?.keyword);
        break;
      case EventTypes.USER_SELECT:
        this.selectUser(event.data?.user);
        break;
      default:
        break;
    }
  }

  // 加载用户列表
  private async loadUsers(): Promise<void> {
    const users = await this.executePagedLoading(
      (page, size) => this.userService.getUserList(page, size, this.state.filterType),
      1,
      this.state.pageSize,
      false
    );

    this.updateState({ items: users });
  }

  // 刷新用户列表
  private async refreshUsers(): Promise<void> {
    this.updateState({ currentPage: 1 });
    await this.loadUsers();
  }

  // 加载更多用户
  private async loadMoreUsers(): Promise<void> {
    if (!this.loadingModel.canLoadMore()) {
      return;
    }

    const users = await this.executePagedLoading(
      (page, size) => this.userService.getUserList(page, size, this.state.filterType),
      this.state.currentPage + 1,
      this.state.pageSize,
      true
    );

    this.updateState({
      items: [...this.state.items, ...users],
      currentPage: this.state.currentPage + 1
    });
  }

  // 搜索用户
  private searchUsers = this.debounce(async (keyword: string) => {
    this.updateState({ searchKeyword: keyword, currentPage: 1 });
    await this.loadUsers();
  }, 500);

  // 选择用户
  private selectUser(user: User): void {
    this.state.selectItem(user);
  }

  // 设置过滤类型
  public setFilterType(type: string): void {
    this.state.setFilterType(type);
    this.sendEvent(new DataEvent(EventTypes.DATA_LOAD));
  }
}

// 3. 在组件中使用
@Component
struct UserListPage {
  private viewModel: UserListViewModel = new UserListViewModel();
  @State private state: UserListState = this.viewModel.getState();
  @State private loadingModel: LoadingModel = this.viewModel.getLoadingModel();

  aboutToAppear(): void {
    // 监听状态变化
    this.viewModel.addStateListener((newState) => {
      this.state = newState;
    });

    // 加载数据
    this.viewModel.sendEvent(new DataEvent(EventTypes.DATA_LOAD));
  }

  aboutToDisappear(): void {
    this.viewModel.onDestroy();
  }

  build() {
    Column() {
      // 搜索框
      TextInput({ placeholder: '搜索用户' })
        .onChange((value) => {
          this.viewModel.sendEvent(new DataEvent(EventTypes.DATA_SEARCH, { keyword: value }));
        })

      // 过滤器
      Row() {
        Button('全部')
          .onClick(() => this.viewModel.setFilterType('all'))
        Button('在线')
          .onClick(() => this.viewModel.setFilterType('online'))
        Button('离线')
          .onClick(() => this.viewModel.setFilterType('offline'))
      }

      // 用户列表
      if (this.loadingModel.isLoading()) {
        LoadingView()
      } else if (this.loadingModel.isFailed()) {
        LoadingFailedView({
          message: this.loadingModel.errorMessage,
          onRetry: () => this.viewModel.sendEvent(new DataEvent(EventTypes.DATA_REFRESH))
        })
      } else {
        List() {
          ForEach(this.state.items, (user: User) => {
            ListItem() {
              UserItemComponent({
                user: user,
                isSelected: this.state.selectedItems.includes(user),
                onSelect: () => {
                  this.viewModel.sendEvent(new UserEvent(EventTypes.USER_SELECT, { user: user }));
                }
              })
            }
          })

          // 加载更多
          if (this.loadingModel.hasMore) {
            ListItem() {
              LoadingMore({ isLoading: this.loadingModel.isLoadingMore })
            }
            .onClick(() => {
              this.viewModel.sendEvent(new DataEvent(EventTypes.DATA_LOAD_MORE));
            })
          }
        }
        .onReachEnd(() => {
          this.viewModel.sendEvent(new DataEvent(EventTypes.DATA_LOAD_MORE));
        })
      }
    }
    .onRefresh(() => {
      this.viewModel.sendEvent(new DataEvent(EventTypes.DATA_REFRESH));
    })
  }
}
```

## 扩展开发指南

### 1. 添加新的工具类

当需要添加新的工具类时，请遵循以下步骤：

1. **在util目录下创建新文件**：
```typescript
// src/main/ets/util/NewUtil.ets
import Logger from './Logger';

const TAG = '[NewUtil]';

export class NewUtil {
  private static instance: NewUtil;

  private constructor() {}

  public static getInstance(): NewUtil {
    if (!NewUtil.instance) {
      NewUtil.instance = new NewUtil();
    }
    return NewUtil.instance;
  }

  // 实现具体功能
  public doSomething(): void {
    Logger.info(TAG, 'Doing something...');
  }
}
```

2. **在Index.ets中导出**：
```typescript
export { NewUtil } from './src/main/ets/util/NewUtil';
```

3. **添加单元测试**：
```typescript
// src/test/ets/util/NewUtil.test.ets
import { describe, it, expect } from '@ohos/hypium';
import { NewUtil } from '../../../main/ets/util/NewUtil';

export default function NewUtilTest() {
  describe('NewUtil', () => {
    it('should create instance', () => {
      const util = NewUtil.getInstance();
      expect(util).not.toBeNull();
    });
  });
}
```

### 2. 添加新的UI组件

创建可复用的UI组件：

1. **在component目录下创建组件**：
```typescript
// src/main/ets/component/NewComponent.ets
import { CommonConstants } from '../constant/CommonConstants';

@Component
export struct NewComponent {
  @Prop title: string = '';
  @Prop @Watch('onDataChange') data: any;
  @State private internalState: boolean = false;

  // 数据变化监听
  onDataChange(): void {
    // 处理数据变化
  }

  // 生命周期
  aboutToAppear(): void {
    // 组件即将出现
  }

  aboutToDisappear(): void {
    // 组件即将消失
  }

  build() {
    Column() {
      Text(this.title)
        .fontSize(16)
        .fontWeight(FontWeight.Medium)
        .margin({ bottom: CommonConstants.SPACE_8 })

      // 组件内容
    }
    .padding(CommonConstants.SPACE_16)
  }
}

// 组件数据接口
export interface NewComponentData {
  id: string;
  name: string;
  value: any;
}
```

2. **在Index.ets中导出**：
```typescript
export { NewComponent, NewComponentData } from './src/main/ets/component/NewComponent';
```

### 3. 添加新的数据模型

创建数据模型类：

1. **在model目录下创建模型**：
```typescript
// src/main/ets/model/NewModel.ets
@Observed
export class NewModel {
  public id: string = '';
  public name: string = '';
  public createdAt: number = Date.now();
  public updatedAt: number = Date.now();

  constructor(data?: Partial<NewModel>) {
    if (data) {
      Object.assign(this, data);
    }
  }

  // 更新数据
  public update(data: Partial<NewModel>): void {
    Object.assign(this, data);
    this.updatedAt = Date.now();
  }

  // 验证数据
  public validate(): boolean {
    return this.id.length > 0 && this.name.length > 0;
  }

  // 转换为JSON
  public toJSON(): any {
    return {
      id: this.id,
      name: this.name,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  // 从JSON创建实例
  public static fromJSON(json: any): NewModel {
    return new NewModel(json);
  }
}
```

### 4. 添加新的常量

在constant目录下添加常量：

```typescript
// src/main/ets/constant/NewConstants.ets
export class NewConstants {
  // 尺寸常量
  public static readonly NEW_WIDTH: number = 200;
  public static readonly NEW_HEIGHT: number = 100;

  // 颜色常量
  public static readonly NEW_COLOR: string = '#FF0000';

  // 文本常量
  public static readonly NEW_TITLE: string = '新标题';

  // 配置常量
  public static readonly NEW_CONFIG = {
    timeout: 5000,
    retryCount: 3,
    enableCache: true
  };
}

// 新的枚举类型
export enum NewEnum {
  TYPE_A = 'type_a',
  TYPE_B = 'type_b',
  TYPE_C = 'type_c'
}
```

## 最佳实践

### 1. 代码规范

- **命名规范**：
  - 类名使用大驼峰命名法（PascalCase）
  - 方法名和变量名使用小驼峰命名法（camelCase）
  - 常量使用全大写加下划线（UPPER_SNAKE_CASE）
  - 文件名使用大驼峰命名法

- **注释规范**：
  - 所有公共API必须添加JSDoc注释
  - 复杂逻辑必须添加行内注释
  - 文件头部添加文件说明注释

- **导入规范**：
  - 系统模块导入放在最前面
  - 第三方模块导入放在中间
  - 项目内部模块导入放在最后
  - 同类型导入按字母顺序排列

### 2. 性能优化

- **懒加载**：
  - 使用LazyForEach进行列表懒加载
  - 大型组件使用动态导入
  - 图片资源使用懒加载

- **内存管理**：
  - 及时清理事件监听器
  - 避免内存泄漏
  - 合理使用对象池

- **渲染优化**：
  - 减少不必要的状态更新
  - 使用@ObjectLink优化对象绑定
  - 合理使用组件缓存

### 3. 错误处理

- **统一错误处理**：
```typescript
export class ErrorHandler {
  public static handle(error: Error, context?: string): void {
    Logger.error('[ErrorHandler]', `Error in ${context}: ${error.message}`);

    // 根据错误类型进行不同处理
    if (error instanceof NetworkError) {
      this.handleNetworkError(error);
    } else if (error instanceof ValidationError) {
      this.handleValidationError(error);
    } else {
      this.handleGenericError(error);
    }
  }

  private static handleNetworkError(error: NetworkError): void {
    // 网络错误处理
  }

  private static handleValidationError(error: ValidationError): void {
    // 验证错误处理
  }

  private static handleGenericError(error: Error): void {
    // 通用错误处理
  }
}
```

### 4. 测试策略

- **单元测试**：
  - 所有工具类必须有单元测试
  - 测试覆盖率不低于80%
  - 使用Mock进行依赖隔离

- **集成测试**：
  - 测试组件间的交互
  - 测试数据流的完整性
  - 测试异常场景

- **UI测试**：
  - 测试组件的渲染
  - 测试用户交互
  - 测试响应式布局

### 5. 文档维护

- **API文档**：
  - 使用JSDoc生成API文档
  - 提供使用示例
  - 说明参数和返回值

- **变更日志**：
  - 记录每次版本的变更
  - 说明破坏性变更
  - 提供迁移指南

## 常见问题解决

### 1. 状态管理问题

**问题**：状态更新不生效
**解决方案**：
- 确保使用@State、@Prop、@ObjectLink等装饰器
- 检查是否正确使用@Observed装饰器
- 避免直接修改数组或对象的属性

### 2. 性能问题

**问题**：列表滚动卡顿
**解决方案**：
- 使用LazyForEach替代ForEach
- 减少列表项的复杂度
- 使用虚拟化滚动

### 3. 内存泄漏问题

**问题**：应用内存持续增长
**解决方案**：
- 检查事件监听器是否正确移除
- 检查定时器是否正确清理
- 使用内存分析工具定位问题

### 4. 网络请求问题

**问题**：网络请求失败
**解决方案**：
- 检查网络权限配置
- 添加网络状态检测
- 实现请求重试机制

## 版本兼容性

### 支持的HarmonyOS版本
- HarmonyOS 4.0+
- API Level 10+

### 依赖的系统能力
- SystemCapability.ArkUI.ArkUI.Full
- SystemCapability.Ability.AbilityRuntime.Core
- SystemCapability.Utils.Lang
- SystemCapability.Hilog

### 升级指南

当升级Common模块版本时：

1. **检查破坏性变更**：
   - 查看CHANGELOG.md
   - 检查API变更
   - 更新代码适配

2. **测试验证**：
   - 运行单元测试
   - 进行集成测试
   - 验证功能完整性

3. **渐进式升级**：
   - 先在开发环境测试
   - 逐步推广到测试环境
   - 最后部署到生产环境

## 贡献指南

### 提交代码

1. **Fork项目**
2. **创建功能分支**
3. **编写代码和测试**
4. **提交Pull Request**

### 代码审查

- 代码风格检查
- 功能测试验证
- 性能影响评估
- 文档完整性检查

### 发布流程

1. **版本号管理**：遵循语义化版本规范
2. **变更日志**：更新CHANGELOG.md
3. **标签发布**：创建Git标签
4. **文档更新**：同步更新文档
