# Security安全实践模块说明文档

## 概述
Security模块是HarmonyOS应用的安全实践功能模块，主要用于展示安全相关的示例代码、最佳实践和教程内容。该模块采用MVVM架构模式，提供了丰富的安全实践案例展示和学习功能，包括示例卡片、分类浏览、详情查看等功能。

## 目录结构

### 根目录文件

#### 配置文件
- **BuildProfile.ets** - 构建配置文件
  - 用途：定义构建时的版本信息、调试模式等常量
  - 调用方法：`import BuildProfile from './BuildProfile'`
  - 数据更改：修改HAR_VERSION、BUILD_MODE_NAME、DEBUG、TARGET_NAME等常量

- **Index.ets** - 模块入口文件
  - 用途：导出模块的主要组件和模型
  - 调用方法：`import { PracticesView, SampleModel, SingleSampleData } from '@ohos/security'`
  - 数据更改：添加新的导出组件时在此文件中添加export语句

- **build-profile.json5** - 构建配置
  - 用途：定义模块的构建选项、混淆规则、目标平台等
  - 调用方法：由构建系统自动读取
  - 数据更改：修改apiType、buildOption、targets等配置项

- **hvigorfile.ts** - Hvigor构建脚本
  - 用途：定义构建任务和插件配置
  - 调用方法：由Hvigor构建系统自动执行
  - 数据更改：在plugins数组中添加自定义插件

- **oh-package.json5** - 包配置文件
  - 用途：定义模块名称、版本、依赖关系
  - 调用方法：由包管理器自动读取
  - 数据更改：使用包管理器命令添加/删除依赖，不建议手动编辑

#### 规则文件
- **consumer-rules.txt** - 消费者混淆规则
  - 用途：定义哪些文件在混淆时需要保持原样
  - 数据更改：添加需要保持的文件路径

- **obfuscation-rules.txt** - 混淆规则配置
  - 用途：定义代码混淆的具体规则和选项
  - 数据更改：修改混淆选项如-enable-property-obfuscation等

### src/main目录结构

#### module.json5
- 用途：定义模块的基本信息和路由映射
- 调用方法：由系统自动读取
- 数据更改：修改模块名称、类型、支持的设备类型、路由映射

#### ets目录 - 主要源码目录

##### view目录 - 页面视图
- **PracticesView.ets** - 实践主视图
  - 用途：安全实践主页面，展示横幅、分类标签、示例卡片
  - 调用方法：作为安全实践页面的主组件使用
  - 数据更改：通过PracticeViewModel管理数据状态
  - 功能特性：支持分类切换、下拉刷新、分页加载、响应式布局

- **PracticeDetailView.ets** - 实践详情视图
  - 用途：展示具体安全实践示例的详细内容页面
  - 调用方法：通过路由导航到实践详情页面
  - 数据更改：通过SampleDetailPageVM管理详情数据
  - 功能特性：支持Web内容展示、代码查看、分享等功能

##### component目录 - UI组件
- **BaseCategoryView.ets** - 基础分类视图
  - 用途：提供分类展示的基础视图组件
  - 调用方法：作为分类视图的基础组件使用
  - 功能特性：统一的分类布局和样式

- **CategorySamples.ets** - 分类示例组件
  - 用途：展示特定分类下的示例内容
  - 调用方法：
    ```typescript
    CategorySamples({
      sampleCategory: this.currentCategory,
      onSampleClick: (sample: SampleCardData) => {
        // 处理示例点击事件
      }
    })
    ```
  - 功能特性：支持不同样式的示例卡片展示、懒加载

- **SampleCard.ets** - 示例卡片组件
  - 用途：展示单个安全实践示例的卡片
  - 调用方法：
    ```typescript
    SampleCard({
      sampleCard: this.sampleData,
      sampleIndex: this.index
    })
    ```
  - 功能特性：支持Web内容预览、分享功能、响应式适配

- **SampleComponent.ets** - 示例组件
  - 用途：通用的示例展示组件
  - 调用方法：在示例卡片中使用
  - 功能特性：统一的示例内容展示格式

- **PictureCard.ets** - 图片卡片组件
  - 用途：展示以图片为主的示例卡片
  - 调用方法：根据卡片样式类型自动使用
  - 功能特性：图片预览、点击放大、懒加载

- **PictureAboveTextCard.ets** - 图片文字卡片组件
  - 用途：展示图片在上方、文字在下方的卡片样式
  - 调用方法：根据卡片样式类型自动使用
  - 功能特性：图文混排、响应式布局

- **SampleListCard.ets** - 示例列表卡片组件
  - 用途：展示列表样式的示例卡片
  - 调用方法：根据卡片样式类型自动使用
  - 功能特性：列表布局、分页加载

- **SampleScrollCard.ets** - 示例滚动卡片组件
  - 用途：展示可滚动的示例卡片
  - 调用方法：根据卡片样式类型自动使用
  - 功能特性：横向滚动、无限滚动

- **TagLabel.ets** - 标签组件
  - 用途：展示示例的标签信息
  - 调用方法：
    ```typescript
    TagLabel({
      tags: this.sampleData.tags,
      maxTags: 3
    })
    ```
  - 功能特性：标签展示、颜色区分、数量限制

##### model目录 - 数据模型
- **SampleData.ets** - 示例数据模型
  - 用途：定义安全实践示例的数据结构
  - 调用方法：`import { SampleData, SampleCategory, SampleCardData } from './model/SampleData'`
  - 数据结构：
    ```typescript
    // 示例内容类
    class SampleContent {
      id: number;                    // 内容ID
      type: CardTypeEnum;            // 内容类型
      mediaType: MediaTypeEnum;      // 媒体类型
      mediaUrl: string;              // 媒体URL
      title: string;                 // 标题
      subTitle: string;              // 副标题
      tags: string[];                // 标签数组
    }
    
    // 示例卡片数据类
    class SampleCardData {
      id: number;                    // 卡片ID
      cardTitle: string;             // 卡片标题
      cardSubTitle: string;          // 卡片副标题
      cardType: CardTypeEnum;        // 卡片类型
      cardStyleType: CardStyleTypeEnum; // 卡片样式类型
      cardImage: string;             // 卡片图片
      version: string;               // 版本信息
      sampleContents: SampleContent[]; // 示例内容数组
      detailCardId: number;          // 详情卡片ID
    }
    
    // 示例分类类
    class SampleCategory {
      loadingModel: LoadingModel;    // 加载模型
      currentPage: number;           // 当前页码
      id: number;                    // 分类ID
      categoryName: string;          // 分类名称
      categoryType: number;          // 分类类型
      tabIcon: string;               // 标签图标
      tabIconSelected: string;       // 选中状态图标
      sampleCards: SampleCardData[]; // 示例卡片数组
    }
    
    // 示例数据类
    class SampleData {
      bannerInfos?: BannerData[];    // 横幅信息
      sampleCategories: SampleCategory[]; // 示例分类数组
    }
    ```

- **SampleDetailData.ets** - 示例详情数据模型
  - 用途：定义示例详情页面的数据结构
  - 调用方法：`import { SingleSampleData, SampleCardDetail } from './model/SampleDetailData'`
  - 数据结构：包含详情内容、代码示例、相关资源等信息

- **SampleModel.ets** - 示例数据模型
  - 用途：管理安全实践示例的数据获取和处理
  - 调用方法：`SampleModel.getInstance()`
  - 主要方法：
    ```typescript
    class SampleModel {
      // 获取示例页面数据
      getSamplePage(currentPage: number, pageSize: number): Promise<ResponseData<SampleData>>
      
      // 获取示例详情
      getSampleDetails(sampleCardId: number): Promise<SampleCardDetail[]>
      
      // 获取单个示例数据
      getSingleSampleData(sampleId: number): Promise<SingleSampleData>
    }
    ```

- **SampleDetailModel.ets** - 示例详情模型
  - 用途：管理示例详情的数据处理
  - 调用方法：在详情页面中使用
  - 主要功能：详情数据获取、缓存管理、状态更新

##### service目录 - 服务层
- **SampleService.ets** - 示例服务
  - 用途：提供安全实践示例的数据请求和缓存服务
  - 调用方法：在Model层中调用服务方法
  - 主要方法：
    ```typescript
    class SampleService {
      // 通过模拟获取示例列表
      getSampleListByMock(): Promise<ResponseData<SampleData>>
      
      // 获取示例页面数据
      getSamplePage(currentPage: number, pageSize: number): Promise<ResponseData<SampleData>>
      
      // 通过偏好设置获取数据
      getSamplePageByPreference(currentPage: number, pageSize: number): Promise<ResponseData<SampleData>>
      
      // 保存数据到偏好设置
      saveSamplePageToPreference(data: SampleData): Promise<void>
      
      // 获取示例详情
      getSampleDetails(sampleCardId: number): Promise<SampleCardDetail[]>
      
      // 获取单个示例数据
      getSingleSampleData(sampleId: number): Promise<SingleSampleData>
    }
    ```

##### viewmodel目录 - 视图模型
- **PracticeViewModel.ets** - 实践视图模型
  - 用途：管理实践页面的状态和业务逻辑
  - 调用方法：`PracticeViewModel.getInstance()`
  - 主要功能：
    - 管理页面状态和数据加载
    - 处理分类切换和示例点击
    - 控制页面导航和跳转
    - 管理横幅和示例数据

- **PracticeState.ets** - 实践状态
  - 用途：定义实践页面的状态数据结构
  - 调用方法：在ViewModel中使用
  - 状态属性：
    - 加载状态
    - 示例数据
    - 分类信息
    - 分页信息

- **SampleDetailPageVM.ets** - 示例详情页面视图模型
  - 用途：管理示例详情页面的状态和交互
  - 调用方法：在示例详情页面中实例化
  - 主要功能：
    - 加载示例详情内容
    - 处理Web内容展示
    - 管理分享和收藏功能

- **SampleDetailState.ets** - 示例详情状态
  - 用途：定义示例详情页面的状态
  - 调用方法：在详情页面ViewModel中使用
  - 状态属性：详情内容、加载状态、Web状态

##### constant目录 - 常量定义
- **CommonConstants.ets** - 通用常量
  - 用途：定义安全模块使用的常量值
  - 调用方法：`import { SampleDetailConstant } from './constant/CommonConstants'`
  - 常量定义：
    ```typescript
    class SampleDetailConstant {
      static PROGRESS_START: number = 0;        // 进度开始值
      static PROGRESS_FINISH: number = 100;     // 进度完成值
      static SUBTITLE_MAXLINE: number = 3;      // 副标题最大行数
      static HOVER_POPUP_LEFT: number = 10;     // 悬停弹窗左偏移量
    }
    ```

##### common目录 - 通用工具
- **SampleConstant.ets** - 示例常量
  - 用途：定义示例相关的常量和枚举
  - 调用方法：`import { SampleConstant, SampleTypeEnum } from './common/SampleConstant'`
  - 常量定义：
    ```typescript
    class SampleConstant {
      static SCROLL_MARGIN_SM: number = -16;    // 小屏滚动边距
      static SCROLL_MARGIN_MD: number = -24;    // 中屏滚动边距
      static SCROLL_MARGIN_LG: number = -32;    // 大屏滚动边距
    }
    
    enum SampleTypeEnum {
      COMMON_CLIENT = 'commonClient',           // 通用客户端
      WEARABLE_CLIENT = 'wearableClient',       // 可穿戴客户端
      COMMON_SAMPLE = 'commonSample',           // 通用示例
      WEARABLE_SAMPLE = 'wearableSample'        // 可穿戴示例
    }
    ```

## 使用方法

### 1. 导入模块
```typescript
import { PracticesView, SampleModel, SingleSampleData } from '@ohos/security';
```

### 2. 使用实践视图
```typescript
@Entry
@Component
struct SecurityPage {
  build() {
    PracticesView()
  }
}
```

### 3. 获取安全实践数据
```typescript
const model = SampleModel.getInstance();
const data = await model.getSamplePage(1, 20);
```

### 4. 处理示例点击事件
```typescript
// 在组件中定义点击处理函数
handleSampleClick = (sampleCard: SampleCardData) => {
  // 跳转到示例详情页面
  router.pushUrl({
    url: 'pages/SampleDetailPage',
    params: {
      sampleCardId: sampleCard.id,
      currentIndex: 0
    }
  });
};
```

## 数据流向

1. **数据获取**：SampleService从网络或本地获取安全实践数据
2. **数据处理**：SampleModel处理和转换数据
3. **状态管理**：PracticeViewModel管理UI状态
4. **视图渲染**：PracticesView根据状态渲染UI
5. **用户交互**：用户操作触发ViewModel中的方法
6. **状态更新**：ViewModel更新状态，触发视图重新渲染

## 扩展开发

### 添加新的安全实践类型
1. 在SampleData.ets中扩展CardTypeEnum枚举
2. 创建对应的组件和视图
3. 在PracticesView中添加新类型的处理逻辑
4. 更新服务层的数据获取逻辑

### 自定义示例卡片样式
1. 创建新的卡片组件
2. 在CategorySamples中注册新组件
3. 更新数据模型支持新的卡片类型
4. 添加相应的样式和交互逻辑

### 添加新的交互功能
1. 在ViewModel中添加新的事件处理方法
2. 在组件中绑定新的交互事件
3. 更新状态管理逻辑
4. 添加相应的UI反馈

## 依赖关系

- @ohos/common - 通用工具和组件
- @ohos/commonbusiness - 通用业务组件
- @kit.AbilityKit - 系统能力工具包
- @kit.BasicServicesKit - 基础服务工具包
- @kit.ArkUI - 方舟UI工具包
- @kit.ArkData - 方舟数据工具包
- @kit.ShareKit - 分享工具包

## 详细组件说明

### 核心视图组件详解

#### PracticesView - 安全实践主视图
**文件位置**：`src/main/ets/view/PracticesView.ets`

**功能描述**：
- 安全实践模块的主页面视图
- 展示安全相关的横幅、分类标签和示例卡片
- 支持响应式布局和多设备适配
- 提供下拉刷新和分页加载功能

**组件属性**：
```typescript
@Component({ freezeWhenInactive: true })
export struct PracticesView {
  viewModel: PracticeViewModel;                    // 视图模型实例
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel; // 全局信息模型
  @StorageProp('systemColorMode') systemColorMode: ConfigurationConstant.ColorMode; // 系统颜色模式
  @State naviStatusHeight: number;                 // 导航状态高度
  @State currentIndex: number;                     // 当前索引
  @State practiceState: PracticeState;             // 实践状态
  @State showTopTab: boolean;                      // 是否显示顶部标签
  private categoryTabController: TabsController;   // 分类标签控制器
  private listScroller: Scroller;                  // 列表滚动器
}
```

**响应式布局配置**：
```typescript
// 根据断点类型设置不同的布局参数
private getLayoutParams(): LayoutParams {
  const breakpoint = this.globalInfoModel.currentBreakpoint;

  switch (breakpoint) {
    case BreakpointTypeEnum.SM:
      return {
        padding: CommonConstants.SPACE_16,
        columns: 1,
        gutter: CommonConstants.SPACE_12
      };
    case BreakpointTypeEnum.MD:
      return {
        padding: CommonConstants.SPACE_24,
        columns: 2,
        gutter: CommonConstants.SPACE_16
      };
    case BreakpointTypeEnum.LG:
    case BreakpointTypeEnum.XL:
      return {
        padding: CommonConstants.SPACE_32,
        columns: 3,
        gutter: CommonConstants.SPACE_20
      };
    default:
      return {
        padding: CommonConstants.SPACE_16,
        columns: 1,
        gutter: CommonConstants.SPACE_12
      };
  }
}
```

**生命周期管理**：
```typescript
// 组件即将出现
aboutToAppear(): void {
  // 初始化视图模型
  this.viewModel = PracticeViewModel.getInstance();

  // 设置导航状态高度
  this.naviStatusHeight = CommonConstants.NAVIGATION_HEIGHT +
    this.globalInfoModel.statusBarHeight;

  // 获取初始状态
  this.practiceState = this.viewModel.getState();

  // 加载初始数据
  this.loadInitialData();
}

// 组件即将消失
aboutToDisappear(): void {
  // 清理资源
  this.viewModel.dispose();
}

// 页面显示
onPageShow(): void {
  // 更新状态栏
  this.viewModel.onPageShow();
}

// 页面隐藏
onPageHide(): void {
  // 处理页面隐藏逻辑
  this.viewModel.onPageHide();
}
```

**事件处理**：
```typescript
// 断点变化处理
handleBreakPointChange(): void {
  this.naviStatusHeight = CommonConstants.NAVIGATION_HEIGHT +
    this.globalInfoModel.statusBarHeight;
  this.viewModel.handleBreakPointChange();
}

// 颜色模式变化处理
handleColorModeChange(): void {
  // 更新主题相关的UI状态
  this.updateThemeColors();
}

// 分类切换处理
handleCategoryChange(index: number): void {
  if (this.currentIndex !== index) {
    this.currentIndex = index;
    this.viewModel.switchCategory(index);
  }
}

// 下拉刷新处理
handleRefresh(): void {
  this.viewModel.refreshData();
}

// 加载更多处理
handleLoadMore(): void {
  this.viewModel.loadMoreData();
}
```

#### PracticeDetailView - 实践详情视图
**文件位置**：`src/main/ets/view/PracticeDetailView.ets`

**功能描述**：
- 展示具体安全实践示例的详细内容
- 支持Web内容展示和代码查看
- 提供分享、收藏等交互功能
- 支持全屏模式和沉浸式体验

**组件结构**：
```typescript
@Component
export struct PracticeDetailView {
  @State sampleDetailState: SampleDetailState;     // 详情状态
  @State webController: WebController;             // Web控制器
  @State isFullScreen: boolean = false;            // 是否全屏
  @State loadingProgress: number = 0;              // 加载进度

  private viewModel: SampleDetailPageVM;           // 详情视图模型
  private shareController: ShareController;        // 分享控制器
}
```

**Web内容展示**：
```typescript
// Web组件配置
Web({
  src: this.sampleDetailState.webUrl,
  controller: this.webController
})
.javaScriptAccess(true)
.domStorageAccess(true)
.fileAccess(true)
.onProgressChange((event) => {
  this.loadingProgress = event.newProgress;
})
.onPageEnd((event) => {
  // 页面加载完成
  this.handlePageLoadComplete(event);
})
.onErrorReceive((event) => {
  // 处理加载错误
  this.handleLoadError(event);
})
```

### 核心组件详解

#### SampleCard - 示例卡片组件
**文件位置**：`src/main/ets/component/SampleCard.ets`

**功能描述**：
- 展示单个安全实践示例的卡片
- 支持多种卡片样式和布局
- 提供Web内容预览功能
- 集成分享和收藏功能

**组件属性**：
```typescript
@Component
export struct SampleCard {
  viewModel: SampleDetailPageVM;                   // 详情视图模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel; // 全局信息模型
  @Consume currentIndex: number;                   // 当前索引
  @Prop sampleIndex: number;                       // 示例索引
  @ObjectLink sampleCard: SampleCardData;         // 示例卡片数据
  @State backIconBgColor: ResourceColor;           // 返回图标背景色
}
```

**卡片样式适配**：
```typescript
// 根据卡片样式类型渲染不同的布局
private buildCardContent(): void {
  switch (this.sampleCard.cardStyleType) {
    case CardStyleTypeEnum.PICTURE_ABOVE_LIST:
      this.buildPictureAboveListCard();
      break;
    case CardStyleTypeEnum.LIST:
      this.buildListCard();
      break;
    case CardStyleTypeEnum.PICTURE:
      this.buildPictureCard();
      break;
    case CardStyleTypeEnum.PICTURE_ABOVE_TEXT:
      this.buildPictureAboveTextCard();
      break;
    case CardStyleTypeEnum.PICTURE_TO_SWIPER:
      this.buildPictureToSwiperCard();
      break;
    default:
      this.buildDefaultCard();
      break;
  }
}
```

**分享功能实现**：
```typescript
// 分享示例内容
private shareContent(): void {
  const shareData: systemShare.SharedData = {
    utd: utd.UniformDataType.HYPERLINK,
    content: {
      title: this.sampleCard.cardTitle,
      abstract: this.sampleCard.cardSubTitle,
      url: this.sampleCard.detailsUrl
    }
  };

  systemShare.share(shareData)
    .then(() => {
      Logger.info(TAG, 'Share success');
    })
    .catch((error: BusinessError) => {
      Logger.error(TAG, 'Share failed', error);
    });
}
```

#### CategorySamples - 分类示例组件
**文件位置**：`src/main/ets/component/CategorySamples.ets`

**功能描述**：
- 展示特定分类下的示例内容
- 支持不同的卡片布局和样式
- 提供懒加载和虚拟化滚动
- 支持下拉刷新和上拉加载

**组件结构**：
```typescript
@Component
export struct CategorySamples {
  @ObjectLink sampleCategory: SampleCategory;      // 示例分类数据
  @State isRefreshing: boolean = false;            // 是否正在刷新
  @State isLoadingMore: boolean = false;           // 是否正在加载更多

  private scroller: Scroller = new Scroller();     // 滚动控制器
  private refreshController: RefreshController;     // 刷新控制器
}
```

**懒加载实现**：
```typescript
// 使用LazyForEach实现懒加载
LazyForEach(this.sampleCategory.sampleCards, (item: SampleCardData, index: number) => {
  SampleCard({
    sampleCard: item,
    sampleIndex: index
  })
}, (item: SampleCardData) => item.id.toString())
```

**下拉刷新实现**：
```typescript
// 下拉刷新组件
Refresh({ refreshing: $$this.isRefreshing, builder: this.refreshBuilder }) {
  // 内容区域
  this.buildContent()
}
.onRefreshing(() => {
  // 执行刷新逻辑
  this.handleRefresh();
})

// 刷新处理
private handleRefresh(): void {
  this.isRefreshing = true;

  // 重置分页
  this.sampleCategory.currentPage = 1;

  // 加载数据
  this.loadCategoryData()
    .finally(() => {
      this.isRefreshing = false;
    });
}
```

### 数据模型详解

#### SampleData - 示例数据模型
**文件位置**：`src/main/ets/model/SampleData.ets`

**核心数据结构**：
```typescript
// 示例内容类 - 定义单个示例的内容信息
export class SampleContent {
  public id: number = 0;                           // 内容唯一标识
  public type: CardTypeEnum = CardTypeEnum.UNKNOWN; // 内容类型（组件/示例/文章等）
  public mediaType: MediaTypeEnum = MediaTypeEnum.IMAGE; // 媒体类型（图片/视频/符号）
  public mediaUrl: string = '';                    // 媒体资源URL
  public title: string = '';                       // 内容标题
  public subTitle: string = '';                    // 内容副标题
  public tags: string[] = [];                      // 标签数组

  // 验证内容数据的有效性
  public isValid(): boolean {
    return this.id > 0 &&
           this.title.length > 0 &&
           this.type !== CardTypeEnum.UNKNOWN;
  }

  // 获取格式化的标签字符串
  public getFormattedTags(): string {
    return this.tags.join(', ');
  }
}

// 示例卡片数据类 - 定义卡片的完整信息
export class SampleCardData {
  public id: number = 0;                           // 卡片唯一标识
  public cardTitle: string = '';                   // 卡片主标题
  public cardSubTitle: string = '';                // 卡片副标题
  public cardType: CardTypeEnum = CardTypeEnum.UNKNOWN; // 卡片类型
  public cardStyleType: CardStyleTypeEnum = CardStyleTypeEnum.LIST; // 卡片样式类型
  public cardImage: string = '';                   // 卡片封面图片
  public version: string = '';                     // 版本信息
  public sampleContents: SampleContent[] = [];     // 示例内容列表
  public detailCardId: number = 0;                 // 详情卡片ID

  // 获取主要内容
  public getPrimaryContent(): SampleContent | null {
    return this.sampleContents.length > 0 ? this.sampleContents[0] : null;
  }

  // 获取所有标签
  public getAllTags(): string[] {
    const allTags: string[] = [];
    this.sampleContents.forEach(content => {
      allTags.push(...content.tags);
    });
    return [...new Set(allTags)]; // 去重
  }

  // 检查是否包含特定标签
  public hasTag(tag: string): boolean {
    return this.getAllTags().includes(tag);
  }
}

// 示例分类类 - 定义分类的完整信息
@Observed
export class SampleCategory {
  public loadingModel: LoadingModel = new LoadingModel(); // 加载状态模型
  public currentPage: number = 1;                  // 当前页码
  public id: number = 0;                          // 分类唯一标识
  public categoryName: string = '';               // 分类名称
  public categoryType: number = 0;                // 分类类型
  public tabIcon: string = '';                    // 标签页图标
  public tabIconSelected: string = '';            // 选中状态图标
  public sampleCards: SampleCardData[] = [];      // 示例卡片列表

  // 添加示例卡片
  public addSampleCard(card: SampleCardData): void {
    this.sampleCards.push(card);
  }

  // 批量添加示例卡片
  public addSampleCards(cards: SampleCardData[]): void {
    this.sampleCards.push(...cards);
  }

  // 清空示例卡片
  public clearSampleCards(): void {
    this.sampleCards = [];
  }

  // 根据ID查找示例卡片
  public findSampleCard(id: number): SampleCardData | undefined {
    return this.sampleCards.find(card => card.id === id);
  }

  // 获取分类的示例总数
  public getTotalSampleCount(): number {
    return this.sampleCards.reduce((total, card) => {
      return total + card.sampleContents.length;
    }, 0);
  }

  // 检查是否有更多数据可加载
  public hasMoreData(): boolean {
    return this.loadingModel.hasMore;
  }

  // 重置分页状态
  public resetPagination(): void {
    this.currentPage = 1;
    this.loadingModel.hasMore = true;
    this.clearSampleCards();
  }
}

// 示例数据类 - 定义完整的示例数据结构
@Observed
export class SampleData {
  public bannerInfos?: BannerData[];               // 横幅信息列表
  public sampleCategories: SampleCategory[] = []; // 示例分类列表

  // 添加分类
  public addCategory(category: SampleCategory): void {
    this.sampleCategories.push(category);
  }

  // 根据ID查找分类
  public findCategory(id: number): SampleCategory | undefined {
    return this.sampleCategories.find(category => category.id === id);
  }

  // 根据类型查找分类
  public findCategoriesByType(type: number): SampleCategory[] {
    return this.sampleCategories.filter(category => category.categoryType === type);
  }

  // 获取所有示例卡片
  public getAllSampleCards(): SampleCardData[] {
    const allCards: SampleCardData[] = [];
    this.sampleCategories.forEach(category => {
      allCards.push(...category.sampleCards);
    });
    return allCards;
  }

  // 搜索示例
  public searchSamples(keyword: string): SampleCardData[] {
    const results: SampleCardData[] = [];
    this.sampleCategories.forEach(category => {
      category.sampleCards.forEach(card => {
        if (card.cardTitle.includes(keyword) ||
            card.cardSubTitle.includes(keyword) ||
            card.hasTag(keyword)) {
          results.push(card);
        }
      });
    });
    return results;
  }
}
```

**枚举类型定义**：
```typescript
// 卡片类型枚举
export enum CardTypeEnum {
  UNKNOWN = 0,        // 未知类型
  COMPONENT = 1,      // 组件类型
  SAMPLE = 2,         // 示例类型
  CODELAB = 3,        // 代码实验室类型
  ARTICLE = 4         // 文章类型
}

// 卡片样式类型枚举
export enum CardStyleTypeEnum {
  LIST = 2,                    // 纯列表样式
  PICTURE = 3,                 // 纯图片样式
  PICTURE_ABOVE_LIST = 1,      // 图片在上方的列表样式
  PICTURE_ABOVE_TEXT = 4,      // 图片在上方的文本样式
  PICTURE_TO_SWIPER = 5        // 图片转轮播样式
}

// 媒体类型枚举
export enum MediaTypeEnum {
  IMAGE = 1,          // 图片类型
  VIDEO = 2,          // 视频类型
  SYMBOL = 3          // 符号图标类型
}
```

**使用示例**：
```typescript
// 创建示例内容
const sampleContent: SampleContent = {
  id: 1001,
  type: CardTypeEnum.COMPONENT,
  mediaType: MediaTypeEnum.IMAGE,
  mediaUrl: 'images/security_component.png',
  title: '安全组件示例',
  subTitle: '展示安全相关的UI组件',
  tags: ['安全', '组件', 'UI']
};

// 创建示例卡片
const sampleCard: SampleCardData = {
  id: 2001,
  cardTitle: '安全实践',
  cardSubTitle: '安全开发最佳实践',
  cardType: CardTypeEnum.SAMPLE,
  cardStyleType: CardStyleTypeEnum.PICTURE_ABOVE_LIST,
  cardImage: 'images/security_practice.png',
  version: '1.0.0',
  sampleContents: [sampleContent],
  detailCardId: 3001
};

// 创建示例分类
const sampleCategory: SampleCategory = {
  loadingModel: new LoadingModel(),
  currentPage: 1,
  id: 4001,
  categoryName: '安全组件',
  categoryType: 1,
  tabIcon: 'ic_security_normal',
  tabIconSelected: 'ic_security_selected',
  sampleCards: [sampleCard]
};

// 创建完整的示例数据
const sampleData: SampleData = {
  bannerInfos: [],
  sampleCategories: [sampleCategory]
};
```
