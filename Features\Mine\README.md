# Mine个人中心模块说明文档

## 概述
Mine模块是HarmonyOS应用的个人中心功能模块，提供用户个人信息管理、应用设置、关于页面等功能。该模块采用MVVM架构模式，实现了响应式布局设计，支持多种设备尺寸和断点适配。

## 目录结构

### 根目录文件

#### 配置文件
- **BuildProfile.ets** - 构建配置文件
  - 用途：定义构建时的版本信息、调试模式等常量
  - 调用方法：`import BuildProfile from './BuildProfile'`
  - 数据更改：修改HAR_VERSION、BUILD_MODE_NAME、DEBUG、TARGET_NAME等常量

- **Index.ets** - 模块入口文件
  - 用途：导出模块的主要视图组件
  - 调用方法：`import { MineView } from '@ohos/mine'`
  - 数据更改：添加新的导出组件时在此文件中添加export语句

- **build-profile.json5** - 构建配置
  - 用途：定义模块的构建选项、混淆规则、目标平台等
  - 调用方法：由构建系统自动读取
  - 数据更改：修改apiType、buildOption、targets等配置项

- **hvigorfile.ts** - Hvigor构建脚本
  - 用途：定义构建任务和插件配置
  - 调用方法：由Hvigor构建系统自动执行
  - 数据更改：在plugins数组中添加自定义插件

- **oh-package.json5** - 包配置文件
  - 用途：定义模块名称、版本、依赖关系
  - 调用方法：由包管理器自动读取
  - 数据更改：使用包管理器命令添加/删除依赖，不建议手动编辑

#### 规则文件
- **consumer-rules.txt** - 消费者混淆规则
  - 用途：定义哪些文件在混淆时需要保持原样
  - 数据更改：添加需要保持的文件路径

- **obfuscation-rules.txt** - 混淆规则配置
  - 用途：定义代码混淆的具体规则和选项
  - 数据更改：修改混淆选项如-enable-property-obfuscation等

### src/main目录结构

#### module.json5
- 用途：定义模块的基本信息和设备类型支持
- 调用方法：由系统自动读取
- 数据更改：修改模块名称、类型、支持的设备类型

#### ets目录 - 主要源码目录

##### component目录 - 组件

- **CardItem.ets** - 卡片项组件
  - 用途：个人中心页面的功能卡片项组件
  - 调用方法：
    ```typescript
    CardItem({
      isShow: this.isShowSheet,
      textContent: $r('app.string.about'),
      symbolSrc: $r('sys.symbol.info_circle'),
      onclick: () => {
        // 点击处理逻辑
      },
      onClose: () => {
        // 关闭处理逻辑
      }
    })
    ```
  - 功能特性：
    - 支持图标和文本显示
    - 集成底部弹窗功能
    - 响应式布局适配
    - 点击和关闭事件处理

- **AboutItemCard.ets** - 关于页面项卡片组件
  - 用途：关于页面中的版本信息卡片
  - 调用方法：在AboutView中使用
  - 功能特性：
    - 显示当前版本信息
    - 版本更新检查和提示
    - 更新进度显示
    - 徽章提示新版本

##### view目录 - 视图

- **MineView.ets** - 个人中心主视图
  - 用途：个人中心页面的主要视图组件
  - 调用方法：作为个人中心页面的根组件使用
  - 功能特性：
    - 顶部导航栏
    - 功能列表展示
    - 响应式布局
    - 弹窗管理

- **AboutView.ets** - 关于页面视图
  - 用途：应用关于信息的展示页面
  - 调用方法：通过AboutBuilder()构建器函数调用
  - 功能特性：
    - 应用图标和名称显示
    - 版本信息展示
    - 版权信息显示
    - 注册信息查看

##### viewmodel目录 - 视图模型

- **MinePageVM.ets** - 个人中心页面视图模型
  - 用途：管理个人中心页面的业务逻辑和状态
  - 调用方法：`MinePageVM.getInstance()`
  - 主要功能：
    - 单例模式管理
    - 弹窗状态控制
    - 事件处理分发

- **MinePageState.ets** - 个人中心页面状态
  - 用途：定义个人中心页面的状态数据
  - 调用方法：在MinePageVM中使用
  - 状态属性：
    - feedbackViewShow：反馈视图显示状态
    - aboutViewShow：关于视图显示状态

- **AboutVM.ets** - 关于页面视图模型
  - 用途：管理关于页面的业务逻辑
  - 调用方法：`AboutVM.getInstance()`
  - 主要功能：
    - 版本检查和更新
    - 浏览器跳转
    - 更新状态管理

- **AboutState.ets** - 关于页面状态
  - 用途：定义关于页面的状态数据
  - 调用方法：在AboutVM中使用
  - 状态属性：
    - currentVersion：当前版本号
    - laterVersionExist：是否存在更新版本
    - isLoadingUpdate：是否正在加载更新

## 使用方法

### 1. 导入模块
```typescript
import { MineView } from '@ohos/mine';
```

### 2. 使用个人中心视图
```typescript
@Entry
@Component
struct MinePage {
  build() {
    MineView()
  }
}
```

### 3. 自定义卡片项
```typescript
CardItem({
  isShow: false,
  textContent: $r('app.string.settings'),
  symbolSrc: $r('sys.symbol.gear'),
  onclick: () => {
    // 处理设置点击
    console.log('Settings clicked');
  },
  onClose: () => {
    // 处理关闭事件
    console.log('Settings sheet closed');
  }
})
```

## 数据流向

1. **页面初始化**：MinePageVM初始化MinePageState
2. **用户交互**：用户点击卡片项触发事件
3. **事件处理**：视图模型处理事件并更新状态
4. **状态更新**：状态变化触发UI重新渲染
5. **弹窗管理**：通过bindSheet管理弹窗显示和隐藏
6. **版本检查**：AboutVM管理版本检查和更新流程

## 扩展开发

### 添加新的功能卡片
1. 在MinePageState中添加新的状态属性
2. 在MinePageVM中添加相应的事件处理
3. 在MineView中添加新的CardItem
4. 创建对应的弹窗视图组件

### 自定义关于页面
1. 修改AboutView中的UI布局
2. 在AboutState中添加新的状态属性
3. 在AboutVM中实现相应的业务逻辑
4. 更新AboutItemCard显示内容

## 依赖关系

- @ohos/common - 通用工具和基础组件
- @kit.AbilityKit - 系统能力工具包
- @kit.BasicServicesKit - 基础服务工具包

## 架构特点

### MVVM模式
- **Model**：状态类定义数据结构
- **View**：视图组件负责UI渲染
- **ViewModel**：视图模型管理业务逻辑和状态

### 单例模式
- MinePageVM和AboutVM都采用单例模式
- 确保全局状态的一致性
- 避免重复创建实例

### 响应式设计
- 支持多种断点类型（SM、MD、LG、XL）
- 自适应布局和样式
- 弹窗类型根据设备尺寸自动调整

### 事件驱动
- 基于事件的状态管理
- 解耦视图和业务逻辑
- 便于扩展和维护

## 详细组件说明

### 核心组件详解

#### MineView - 个人中心主视图
**文件位置**：`src/main/ets/view/MineView.ets`

**功能描述**：
- 个人中心页面的主要视图组件
- 提供顶部导航栏和功能列表
- 支持响应式布局和弹窗管理
- 集成节流函数优化性能

**属性配置**：
```typescript
@Component
export struct MineView {
  viewModel: MinePageVM = MinePageVM.getInstance();           // 视图模型实例
  @State minePageState: MinePageState = this.viewModel.getState(); // 页面状态
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel; // 全局信息模型
}
```

**布局结构**：
```typescript
Column() {
  // 顶部导航栏
  TopNavigationView({
    topNavigationData: {
      fontSize: new BreakpointType({
        sm: $r('sys.float.Title_M'),
        md: $r('sys.float.Title_L'),
        lg: $r('sys.float.Title_L'),
        xl: $r('sys.float.Title_S'),
      }).getValue(this.globalInfoModel.currentBreakpoint),
      bgColor: $r('sys.color.background_secondary'),
      title: $r('app.string.mine_title'),
      isFullScreen: true,
    }
  });

  // 功能列表
  List() {
    ListItemGroup() {
      ListItem({ style: ListItemStyle.CARD }) {
        CardItem({
          isShow: this.minePageState.aboutViewShow,
          textContent: $r('app.string.about'),
          symbolSrc: $r('sys.symbol.info_circle'),
          onclick: () => {
            this.viewModel.sendEvent(new AboutBindSheetEvent(true));
          },
          onClose: () => {
            this.viewModel.sendEvent(new AboutBindSheetEvent(false));
          },
        })
      }
    }
  }
}
```

**性能优化**：
```typescript
// 节流函数实现
throttle(func: Function, interval: number) {
  let lastTime = 0;
  return () => {
    const nowTime = Date.now();
    const remainTime = interval - (nowTime - lastTime);
    if (remainTime <= 0) {
      lastTime = nowTime;
      func();
    }
  };
}
```

#### CardItem - 卡片项组件
**文件位置**：`src/main/ets/component/CardItem.ets`

**功能描述**：
- 个人中心功能项的卡片展示组件
- 支持图标、文本和右箭头显示
- 集成底部弹窗功能
- 响应式弹窗类型适配

**属性配置**：
```typescript
@Component
export struct CardItem {
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel;
  @Prop isShow: boolean;                    // 弹窗显示状态
  textContent?: Resource;                   // 文本内容资源
  symbolSrc?: Resource;                     // 图标资源
  onclick: Function = () => {};             // 点击回调
  onClose: Function = () => {};             // 关闭回调
}
```

**弹窗配置**：
```typescript
.bindSheet(this.isShow, AboutBuilder(), {
  // 根据断点类型设置弹窗类型
  preferType: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ?
    SheetType.BOTTOM : SheetType.CENTER,
  title: { title: $r('app.string.about') },
  // 根据断点类型设置弹窗高度
  height: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
    ((this.globalInfoModel.deviceHeight - this.globalInfoModel.decorHeight) *
    CommonConstants.SHEET_HEIGHT_RATIO_XL) : SheetSize.LARGE,
  // 根据断点类型设置弹窗宽度
  width: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
    CommonConstants.SHEET_WIDTH_XL : undefined,
  onWillDisappear: () => {
    this.onClose();
  },
})
```

#### AboutView - 关于页面视图
**文件位置**：`src/main/ets/view/AboutView.ets`

**功能描述**：
- 应用关于信息的展示页面
- 显示应用图标、名称和版本信息
- 提供版权信息和注册信息查看
- 集成版本检查和更新功能

**布局结构**：
```typescript
Column() {
  // 上部分：应用信息
  Column() {
    Image($r('app.media.app_icon'))
      .draggable(false)
      .width($r('app.float.about_image_size'))
      .height($r('app.float.about_image_size'))
      .borderRadius($r('sys.float.corner_radius_level8'))

    Text($r('app.string.app_name'))
      .fontSize($r('sys.float.Title_S'))
      .fontWeight(FontWeight.Bold)
      .margin({ top: $r('sys.float.padding_level4') })

    AboutItemCard()
      .margin({ top: $r('sys.float.padding_level16') })
  }

  // 下部分：版权信息
  Column() {
    Text($r('app.string.copyright1'))
      .fontWeight(FontWeight.Medium)
      .fontColor($r('sys.color.font_emphasize'))
      .fontSize($r('sys.float.Body_S'))
      .onClick(() => {
        this.viewModel.sendEvent(new ViewRegistrationInfoEvent());
      })

    Text($r('app.string.copyright2'))
      .fontColor($r('sys.color.font_secondary'))
      .fontWeight(FontWeight.Regular)
      .fontSize($r('sys.float.Body_S'))
  }
}
.justifyContent(FlexAlign.SpaceBetween)
```

#### AboutItemCard - 关于页面项卡片
**文件位置**：`src/main/ets/component/AboutItemCard.ets`

**功能描述**：
- 显示应用版本信息
- 检查和提示版本更新
- 处理版本更新操作
- 显示更新进度状态

**版本信息显示**：
```typescript
// 版本信息区域
Column() {
  if (this.aboutState.laterVersionExist) {
    // 有新版本时显示徽章
    Badge({
      value: '',
      style: { badgeSize: 6, badgeColor: $r('app.color.about_badge_color') },
      position: BadgePosition.Right,
    }) {
      Text($r('app.string.version_information'))
        .fontWeight(FontWeight.Medium)
        .fontSize($r('sys.float.Body_L'))
        .fontColor($r('sys.color.font_primary'))
        .margin({ right: $r('sys.float.padding_level6') })
    }
  } else {
    // 无新版本时普通显示
    Text($r('app.string.version_information'))
      .fontWeight(FontWeight.Medium)
      .fontSize($r('sys.float.Body_L'))
      .fontColor($r('sys.color.font_primary'))
  }

  // 当前版本号
  Text(AppStorage.get<BundleInfoData>('BundleInfoData')?.versionName as string)
    .margin({ top: $r('sys.float.padding_level2') })
    .fontWeight(FontWeight.Regular)
    .fontSize($r('sys.float.Subtitle_S'))
    .fontColor($r('sys.color.font_secondary'))
}
```

**更新状态显示**：
```typescript
// 右侧状态区域
Row() {
  if (!this.aboutState.isLoadingUpdate) {
    // 非加载状态显示文本和箭头
    Text(this.aboutState.laterVersionExist ?
      $r('app.string.updated_version') : $r('app.string.latest_version'))
      .fontWeight(FontWeight.Regular)
      .fontSize($r('sys.float.Subtitle_S'))
      .fontColor($r('sys.color.font_secondary'))
      .margin({ right: $r('sys.float.padding_level2') })

    SymbolGlyph($r('sys.symbol.chevron_right'))
      .fontSize($r('sys.float.Title_S'))
      .fontColor([$r('sys.color.icon_fourth')])
  } else {
    // 加载状态显示进度条
    LoadingProgress()
      .width($r('app.float.about_loadingProgress_size'))
      .height($r('app.float.about_loadingProgress_size'))
  }
}
.onClick(() => {
  if (this.aboutState.laterVersionExist) {
    this.viewModel.sendEvent(new UpdateVersionEvent());
  }
})
```

### 视图模型详解

#### MinePageVM - 个人中心页面视图模型
**文件位置**：`src/main/ets/viewmodel/MinePageVM.ets`

**设计模式**：单例模式
```typescript
export class MinePageVM extends BaseVM<MinePageState> {
  private static instance: MinePageVM;

  public static getInstance() {
    if (!MinePageVM.instance) {
      MinePageVM.instance = new MinePageVM();
    }
    return MinePageVM.instance;
  }

  private constructor() {
    super(new MinePageState());
  }
}
```

**事件处理**：
```typescript
public sendEvent(event: BaseVMEvent): void {
  if (event instanceof AboutBindSheetEvent) {
    // 处理关于页面弹窗事件
    this.state.aboutViewShow = event.dataValue;
  } else if (event instanceof FeedbackBindSheetEvent) {
    // 处理反馈页面弹窗事件
    this.state.feedbackViewShow = event.dataValue;
  }
}
```

#### AboutVM - 关于页面视图模型
**文件位置**：`src/main/ets/viewmodel/AboutVM.ets`

**核心功能**：
```typescript
export class AboutVM extends BaseVM<AboutState> {
  private static instance: AboutVM;
  private updateService = UpdateService.getInstance();

  // 版本检查
  private checkVersion(): void {
    this.updateService.checkUpdate().then((existNewVersion: boolean) => {
      this.state.laterVersionExist = existNewVersion;
      this.state.currentVersion = AppStorage.get<BundleInfoData>('BundleInfoData')?.versionName as string;
    });
  }

  // 版本更新
  private updateVersion(): void {
    this.state.isLoadingUpdate = true;
    this.updateService.updateVersion().then(() => {
      this.state.isLoadingUpdate = false;
    });
  }

  // 跳转浏览器
  private jumpToBrowser(context: common.UIAbilityContext): void {
    const want: Want = {
      action: 'ohos.want.action.viewData',
      entities: ['entity.system.browsable'],
      uri: ResourceUtil.getRawFileStringByKey(getContext(), ConfigMapKey.MIIT_URL),
    };
    context.startAbility(want)
      .then(() => {
        Logger.info(TAG, 'Start browsableAbility successfully.');
      })
      .catch((err: BusinessError) => {
        Logger.error(TAG, `Failed to startAbility. Code: ${err.code}, message: ${err.message}`);
      });
  }
}
```

**事件处理**：
```typescript
sendEvent(event: BaseVMEvent) {
  if (event instanceof CheckVersionEvent) {
    this.checkVersion();
  } else if (event instanceof UpdateVersionEvent) {
    this.updateVersion();
  } else if (event instanceof ViewRegistrationInfoEvent) {
    const context: common.UIAbilityContext = getContext() as common.UIAbilityContext;
    this.jumpToBrowser(context);
  }
}
```

### 状态管理详解

#### MinePageState - 个人中心页面状态
**文件位置**：`src/main/ets/viewmodel/MinePageState.ets`

**状态定义**：
```typescript
@Observed
export class MinePageState extends BaseState {
  public feedbackViewShow: boolean = false;    // 反馈视图显示状态
  public aboutViewShow: boolean = false;       // 关于视图显示状态
}
```

**使用示例**：
```typescript
// 在视图中监听状态变化
@State minePageState: MinePageState = this.viewModel.getState();

// 根据状态控制弹窗显示
CardItem({
  isShow: this.minePageState.aboutViewShow,
  // 其他属性...
})
```

#### AboutState - 关于页面状态
**文件位置**：`src/main/ets/viewmodel/AboutState.ets`

**状态定义**：
```typescript
@Observed
export class AboutState extends BaseState {
  public currentVersion: string = '1.0.0';         // 当前版本号
  public laterVersionExist: boolean = false;       // 是否存在更新版本
  public isLoadingUpdate: boolean = false;         // 是否正在加载更新

  public constructor() {
    super();
  }
}
```

**状态使用场景**：
```typescript
// 版本信息显示
Text(this.aboutState.currentVersion)

// 更新提示显示
if (this.aboutState.laterVersionExist) {
  Badge({ /* 徽章配置 */ })
}

// 加载状态显示
if (this.aboutState.isLoadingUpdate) {
  LoadingProgress()
}
```

## 事件系统

### 事件类型定义

#### AboutBindSheetEvent - 关于页面弹窗事件
```typescript
export class AboutBindSheetEvent implements BaseVMEvent {
  readonly dataValue: boolean;

  constructor(dataValue: boolean) {
    this.dataValue = dataValue;
  }
}
```

#### FeedbackBindSheetEvent - 反馈弹窗事件
```typescript
export class FeedbackBindSheetEvent implements BaseVMEvent {
  readonly dataValue: boolean;

  constructor(dataValue: boolean) {
    this.dataValue = dataValue;
  }
}
```

#### CheckVersionEvent - 检查版本事件
```typescript
export class CheckVersionEvent implements BaseVMEvent {
}
```

#### UpdateVersionEvent - 更新版本事件
```typescript
export class UpdateVersionEvent implements BaseVMEvent {
}
```

#### ViewRegistrationInfoEvent - 查看注册信息事件
```typescript
export class ViewRegistrationInfoEvent implements BaseVMEvent {
}
```

### 事件使用示例

```typescript
// 显示关于页面弹窗
this.viewModel.sendEvent(new AboutBindSheetEvent(true));

// 隐藏关于页面弹窗
this.viewModel.sendEvent(new AboutBindSheetEvent(false));

// 检查版本更新
this.viewModel.sendEvent(new CheckVersionEvent());

// 执行版本更新
this.viewModel.sendEvent(new UpdateVersionEvent());

// 查看注册信息
this.viewModel.sendEvent(new ViewRegistrationInfoEvent());
```

## 响应式设计

### 断点适配

**字体大小适配**：
```typescript
fontSize: new BreakpointType({
  sm: $r('sys.float.Title_M'),      // 小屏
  md: $r('sys.float.Title_L'),      // 中屏
  lg: $r('sys.float.Title_L'),      // 大屏
  xl: $r('sys.float.Title_S'),      // 超大屏
}).getValue(this.globalInfoModel.currentBreakpoint)
```

**内边距适配**：
```typescript
.padding(new BreakpointType<Padding>({
  sm: {
    left: $r('sys.float.padding_level8'),
    right: $r('sys.float.padding_level8')
  },
  md: {
    left: $r('sys.float.padding_level12'),
    right: $r('sys.float.padding_level12')
  },
  lg: {
    left: $r('sys.float.padding_level16'),
    right: $r('sys.float.padding_level16')
  },
}).getValue(this.globalInfoModel.currentBreakpoint))
```

**弹窗类型适配**：
```typescript
preferType: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ?
  SheetType.BOTTOM : SheetType.CENTER
```

**弹窗尺寸适配**：
```typescript
height: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
  ((this.globalInfoModel.deviceHeight - this.globalInfoModel.decorHeight) *
  CommonConstants.SHEET_HEIGHT_RATIO_XL) : SheetSize.LARGE,

width: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
  CommonConstants.SHEET_WIDTH_XL : undefined
```

### 布局适配

**侧边栏适配**：
```typescript
.padding({
  left: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ?
    CommonConstants.TAB_BAR_WIDTH : 0,
})
```

## 性能优化

### 1. 单例模式优化
```typescript
// 避免重复创建视图模型实例
export class MinePageVM extends BaseVM<MinePageState> {
  private static instance: MinePageVM;

  public static getInstance() {
    if (!MinePageVM.instance) {
      MinePageVM.instance = new MinePageVM();
    }
    return MinePageVM.instance;
  }
}
```

### 2. 节流函数优化
```typescript
// 防止频繁触发事件
throttle(func: Function, interval: number) {
  let lastTime = 0;
  return () => {
    const nowTime = Date.now();
    const remainTime = interval - (nowTime - lastTime);
    if (remainTime <= 0) {
      lastTime = nowTime;
      func();
    }
  };
}
```

### 3. 状态管理优化
```typescript
// 使用@Observed装饰器确保状态变化能被监听
@Observed
export class MinePageState extends BaseState {
  // 状态属性...
}

// 在组件中使用@State监听状态变化
@State minePageState: MinePageState = this.viewModel.getState();
```

### 4. 图片优化
```typescript
// 禁用图片拖拽，提升性能
Image($r('app.media.app_icon'))
  .draggable(false)
  .width($r('app.float.about_image_size'))
  .height($r('app.float.about_image_size'))
```

## 扩展开发指南

### 1. 添加新的功能卡片

**步骤1：扩展状态**
```typescript
// 在MinePageState中添加新状态
@Observed
export class MinePageState extends BaseState {
  public feedbackViewShow: boolean = false;
  public aboutViewShow: boolean = false;
  public settingsViewShow: boolean = false;  // 新增设置页面状态
}
```

**步骤2：添加事件类**
```typescript
// 定义新的事件类
export class SettingsBindSheetEvent implements BaseVMEvent {
  readonly dataValue: boolean;

  constructor(dataValue: boolean) {
    this.dataValue = dataValue;
  }
}
```

**步骤3：扩展视图模型**
```typescript
// 在MinePageVM中处理新事件
public sendEvent(event: BaseVMEvent): void {
  if (event instanceof AboutBindSheetEvent) {
    this.state.aboutViewShow = event.dataValue;
  } else if (event instanceof FeedbackBindSheetEvent) {
    this.state.feedbackViewShow = event.dataValue;
  } else if (event instanceof SettingsBindSheetEvent) {
    this.state.settingsViewShow = event.dataValue;  // 新增处理
  }
}
```

**步骤4：添加UI组件**
```typescript
// 在MineView中添加新的CardItem
ListItem({ style: ListItemStyle.CARD }) {
  CardItem({
    isShow: this.minePageState.settingsViewShow,
    textContent: $r('app.string.settings'),
    symbolSrc: $r('sys.symbol.gear'),
    onclick: () => {
      this.viewModel.sendEvent(new SettingsBindSheetEvent(true));
    },
    onClose: () => {
      this.viewModel.sendEvent(new SettingsBindSheetEvent(false));
    },
  })
}
```

### 2. 自定义关于页面内容

**添加新的信息项**：
```typescript
// 在AboutView中添加新的信息展示
Column() {
  // 现有内容...

  // 新增开发者信息
  Text($r('app.string.developer_info'))
    .fontSize($r('sys.float.Body_M'))
    .fontColor($r('sys.color.font_secondary'))
    .margin({ top: $r('sys.float.padding_level8') })

  // 新增联系方式
  Text($r('app.string.contact_info'))
    .fontSize($r('sys.float.Body_S'))
    .fontColor($r('sys.color.font_tertiary'))
    .margin({ top: $r('sys.float.padding_level4') })
    .onClick(() => {
      // 处理联系方式点击
    })
}
```

**扩展版本信息**：
```typescript
// 在AboutItemCard中添加更多版本信息
Column() {
  // 现有版本信息...

  // 新增构建信息
  Text(`Build: ${BuildProfile.BUILD_MODE_NAME}`)
    .fontSize($r('sys.float.Caption_L'))
    .fontColor($r('sys.color.font_tertiary'))
    .margin({ top: $r('sys.float.padding_level1') })

  // 新增目标平台
  Text(`Target: ${BuildProfile.TARGET_NAME}`)
    .fontSize($r('sys.float.Caption_L'))
    .fontColor($r('sys.color.font_tertiary'))
}
```

### 3. 集成第三方服务

**添加反馈功能**：
```typescript
// 创建反馈视图模型
export class FeedbackVM extends BaseVM<FeedbackState> {
  private static instance: FeedbackVM;

  public static getInstance(): FeedbackVM {
    if (!FeedbackVM.instance) {
      FeedbackVM.instance = new FeedbackVM();
    }
    return FeedbackVM.instance;
  }

  public sendFeedback(content: string): void {
    // 实现反馈发送逻辑
    this.state.isSubmitting = true;

    // 调用反馈服务
    FeedbackService.submitFeedback(content)
      .then(() => {
        this.state.isSubmitting = false;
        this.state.submitSuccess = true;
      })
      .catch((error) => {
        this.state.isSubmitting = false;
        this.state.errorMessage = error.message;
      });
  }
}
```

## 测试指南

### 1. 单元测试

**视图模型测试**：
```typescript
describe('MinePageVM', () => {
  let viewModel: MinePageVM;

  beforeEach(() => {
    viewModel = MinePageVM.getInstance();
  });

  it('should handle AboutBindSheetEvent correctly', () => {
    const event = new AboutBindSheetEvent(true);
    viewModel.sendEvent(event);

    expect(viewModel.getState().aboutViewShow).toBe(true);
  });

  it('should handle FeedbackBindSheetEvent correctly', () => {
    const event = new FeedbackBindSheetEvent(false);
    viewModel.sendEvent(event);

    expect(viewModel.getState().feedbackViewShow).toBe(false);
  });
});
```

**状态测试**：
```typescript
describe('MinePageState', () => {
  it('should initialize with default values', () => {
    const state = new MinePageState();

    expect(state.aboutViewShow).toBe(false);
    expect(state.feedbackViewShow).toBe(false);
  });
});
```

### 2. 集成测试

**组件渲染测试**：
```typescript
describe('MineView Integration', () => {
  it('should render correctly', () => {
    const component = render(MineView());

    expect(component.findByText('关于')).toBeTruthy();
    expect(component.findByRole('button')).toBeTruthy();
  });

  it('should show about sheet when clicked', async () => {
    const component = render(MineView());
    const aboutButton = component.findByText('关于');

    fireEvent.click(aboutButton);

    await waitFor(() => {
      expect(component.findByText('版本信息')).toBeTruthy();
    });
  });
});
```

### 3. 端到端测试

**用户流程测试**：
```typescript
describe('Mine Page E2E', () => {
  it('should complete about flow', async () => {
    // 1. 打开个人中心页面
    await page.goto('/mine');

    // 2. 点击关于按钮
    await page.click('[data-testid="about-button"]');

    // 3. 验证关于页面显示
    await expect(page.locator('[data-testid="about-sheet"]')).toBeVisible();

    // 4. 检查版本信息
    await expect(page.locator('[data-testid="version-info"]')).toContainText('1.0.0');

    // 5. 关闭关于页面
    await page.click('[data-testid="close-button"]');

    // 6. 验证关于页面隐藏
    await expect(page.locator('[data-testid="about-sheet"]')).toBeHidden();
  });
});
```

## 常见问题解决

### 1. 弹窗不显示问题

**问题现象**：点击卡片项后弹窗没有显示

**可能原因**：
- 状态更新没有触发
- bindSheet配置错误
- 全局信息模型未初始化

**解决方案**：
```typescript
// 检查状态更新
console.log('About view show:', this.minePageState.aboutViewShow);

// 确保全局信息模型已初始化
@StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel =
  AppStorage.get('GlobalInfoModel') || new GlobalInfoModel();

// 检查bindSheet配置
.bindSheet(this.isShow, AboutBuilder(), {
  preferType: SheetType.BOTTOM,  // 简化配置进行测试
  title: { title: '关于' },
  height: SheetSize.MEDIUM,
})
```

### 2. 版本检查失败问题

**问题现象**：版本检查功能不工作

**可能原因**：
- 更新服务未正确初始化
- 网络权限不足
- 服务端接口异常

**解决方案**：
```typescript
// 添加错误处理
private checkVersion(): void {
  this.updateService.checkUpdate()
    .then((existNewVersion: boolean) => {
      this.state.laterVersionExist = existNewVersion;
      this.state.currentVersion = AppStorage.get<BundleInfoData>('BundleInfoData')?.versionName as string;
    })
    .catch((error: Error) => {
      Logger.error(TAG, 'Check version failed:', error);
      // 设置默认状态
      this.state.laterVersionExist = false;
    });
}
```

### 3. 响应式布局异常

**问题现象**：在不同设备上布局显示异常

**可能原因**：
- 断点监听未正确设置
- BreakpointType配置不完整

**解决方案**：
```typescript
// 确保断点监听正确设置
@StorageProp('GlobalInfoModel') @Watch('onBreakpointChange')
globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;

onBreakpointChange(): void {
  // 断点变化时的处理逻辑
  console.log('Breakpoint changed:', this.globalInfoModel.currentBreakpoint);
}

// 提供完整的断点配置
fontSize: new BreakpointType({
  sm: $r('sys.float.Title_M'),
  md: $r('sys.float.Title_L'),
  lg: $r('sys.float.Title_L'),
  xl: $r('sys.float.Title_S'),
}).getValue(this.globalInfoModel.currentBreakpoint || BreakpointTypeEnum.SM)
```

## 最佳实践

### 1. 代码组织
- 按功能模块划分文件结构
- 使用清晰的命名约定
- 保持组件的单一职责
- 合理使用TypeScript类型定义

### 2. 状态管理
- 使用@Observed装饰器确保状态可观察
- 避免直接修改状态对象
- 通过事件系统管理状态变化
- 合理设计状态的粒度

### 3. 性能优化
- 使用单例模式避免重复创建实例
- 实现节流函数防止频繁操作
- 合理使用图片资源和缓存
- 避免不必要的重渲染

### 4. 用户体验
- 提供清晰的视觉反馈
- 支持多种设备尺寸适配
- 实现流畅的动画效果
- 处理异常情况和错误状态

## 版本兼容性

### 支持的HarmonyOS版本
- HarmonyOS 4.0+
- API Level 10+

### 依赖的系统能力
- SystemCapability.ArkUI.ArkUI.Full
- SystemCapability.Ability.AbilityRuntime.Core
- SystemCapability.Utils.Lang

### 升级指南

当升级Mine模块版本时：

1. **检查API变更**：查看CHANGELOG.md了解破坏性变更
2. **更新依赖**：确保@ohos/common模块版本兼容
3. **测试验证**：运行现有的单元测试和集成测试
4. **UI验证**：验证在不同设备上的显示效果

---

**注意**：本文档会随着模块的更新而持续维护，请关注版本变更和最新的使用指南。如有问题或建议，请通过项目的Issue系统反馈。
```
```
